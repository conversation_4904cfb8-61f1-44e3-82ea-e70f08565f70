**Markdown parser and generator written in Kotlin**

Project setup
-------------
#### Run with Docker
1. Install docker if you don't have it already
2. Copy the .env.example file to .env
3. Run `docker-compose up --build` in the root of the project
4. Run `docker-compose exec app npm run migrate` to run the migrations
5. Run `docker-compose exec app npm run seed` to run the seeders

App folder structure
-----------------
1. **config** - Put here all the config files used in the application, like for ex: database config, or third party credentials
2. **lib** - Librarys used through whole application:
    1. db.js - For the database we use Sequelize which is a promise-based Node.js ORM
    2. emitter.js - It is extending node js `EventEmitter` (here we register all the listeners and we add here list of constants for the event names)
    3. cronjobRunner.js - It is class were we register all the cron jobs in the app (`node schedule` it used for scheduling the jobs and by default it is nto installed)
3. **migrations** - Sequelize migrations
4. **seeders** - Sequelize seeders
5. **models** - Sequelize models
6. **modules** - In this folder we hold the business logic for the application, separated in domain of featured based modules. Each module should have an
    1. order.js file where we define what method is this module exporting , and tend to communicate with other modules only through events and their index files,<br/>
    do not directly access for example other module`s repository files, the modules need to be as independent as possible
    2. repository folder
    3. listeners (optional) folder - we define here all the listeners for the module which must implement `ListenerInterface.js`
    4. jobs (optional) folder - we define here all the cron jobs for the module which must implement `JobInterface.js`
    5. boot.js (optional) file - we define here things like registering event listeners and cron jobs (_see example-module_).
8. **modules/core**
    1. errors - commonly used errors through the app.
    2. interfaces - here `ListenerInterface.js` and  `JobInterface.js` can be found add here if required any additional commonly used interfaces
    3. factories - for now it is only repository-factory

Repository Factory
-----------------
The main purpose of repository factory is to not rewrite all commonly used function (like `findOneById`,`removeById`, `findOneByFields`,`findByFieldsAndCountAll`...) <br/>
for every new Sequelize model<br/>
The only thing that needs to be done is to create new repository using the repository factory and passing the appropriate Sequelize model,
then export this new repository and you can use the base repository methods , add new ones or overwrite the existing one if needed.<br/>
Most of the methods inside the repository factories are expecting the following parameters: <br/>
* fields   - this is equal to `where` in Sequelize
* options  - here you can add `scopes` as part of the options  and other Sequelize options, like `subQuery`, `distinct` ...
* includes - here you can define list of  associations that you want to be included in the query
* pagination - this parameter is required if you want to paginate the results ,example for pagination param<br/>
    ``{
           page: 1
           rowsPerPage: 10
          sortBy: 'createdAt'
          descending: true
     }``
     <br/>The result returned when using pagination is
     ``{
         result:Array,
         pagination:
         {rowsNumber: Int   rowsPerPage: Int    page: Int   sortBy: String  descending: Boolean}
     }
     ``


