# URL of the frontend root
PUBLIC_HOST_URL=http://localhost:3000

# URL of the backend root
APP_URL=http://localhost:3000
APP_ENV=local
PORT=3000

# Database configuration
DB_HOST=postgres
DB_USER=postgres
DB_PASS=postgres
DB_NAME=tixpi
DB_PORT=5432
DB_SSL=false


# Locize configuration
LOCIZE_PROJECT_ID=7d766200-3683-4c09-9106-e0c4af605051
LOCIZE_API_KEY=9f3c6055-f79f-4300-9bf9-6e2a7f1b8a74
LOCIZE_VERSION=latest

# AWS LOCATION CONFIG
AWS_REGION=us-east-1
AWS_CREDENTIALS=eu-central-1:0cde9253-1260-41a1-b6fc-48cdc2f61627

# Email configuration
SMTP_HOST=sandbox.smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=8f2c72b4a507af
SMTP_PASS=c45d0b3afb3546
SMTP_SECURE=false


#saferpay
SAFER_PAY_CUSTOMER_ID=262987
SAFER_PAY_TERMINAL_ID=17751204
SAFER_PAY_TOKEN=QVBJXzI2Mjk4N183MjQ1ODM0OToyM2JoeSY4b3AwX3M3OFVqZG1rbF8wOXM=

#stripe
STRIPE_SECRET_KEY=rk_test_51QvEb1FgB8ZYiLowM2Y9x6MUJzGuPkl8V4ASQSEd9zQHtBhlaGhNZrHSGu48nT7zxjtJ8NwXMEpTh4xFhGeeG9Hb00joi5HkbH
STRIPE_WEBHOOK_SECRET=whsec_f60134ac7a11948ba95cea21f63569154ac538d6ba62d8b9aec4a042f4d5a6a1

#job queue
MOVER_SEND_OFFER_EMAIL_BATCH_SIZE=2
START_CHECK_MOVER_OFFER_ACCEPTANCE_AFTER=30

#common
BUSINESS_DAYS_TILL_MOVING_DATE=6
TIME_FOR_BUSINESS_TO_RESOLVE_ROUTE_CANCELATION='1 minute'

# Tixpi specific
TIXPI_ITEM_CATEGORY_ID=2,28

# Meilisearch
MEILISEARCH_HOST=https://edge.meilisearch.com
MEILISEARCH_API_KEY=58a9ab7a900462e8df372cd858c3a0697085e743
MEILISEARCH_ENABLED=true
MEILISEARCH_INDEX=items-local