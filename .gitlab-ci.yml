image: node:alpine

before_script:
  - apt-get update -y
  - npm install -g pm2

stages:
  - deploy
#end add

deploy_project:
  stage: deploy
  before_script:
    # Install ssh-agent if not already installed, it is required by Docker.
    # (change apt-get to yum if you use a CentOS-based image)
    - 'which ssh-agent || ( apk add --update openssh )'

    # Add bash
    - apk add --update bash

    # Add git
    - apk add --update git

    # Add pm2
    - npm install -g pm2

    # Run ssh-agent (inside the build environment)
    - eval $(ssh-agent -s)

    # Add the SSH key stored in SSH_PRIVATE_KEY variable to the agent store
    - echo "$TIXPI_STAGING_AWS_PEM" | ssh-add -

    # For Docker builds disable host key checking. Be aware that by adding that
    # you are suspectible to man-in-the-middle attacks.
    # WARNING: Use this only with the Docker executor, if you use it with shell
    # you will overwrite your user's SSH config.
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    # In order to properly check the server's host key, assuming you created the
    # SSH_SERVER_HOSTKEYS variable previously, uncomment the following two lines
    # instead.
    # - mkdir -p ~/.ssh
    # - '[[ -f /.dockerenv ]] && echo "$SSH_SERVER_HOSTKEYS" > ~/.ssh/known_hosts'
  environment:
    name: staging
  script:
    - cp deploy/ecosystem.config.tixpi-staging.js ecosystem.config.js
    - pm2 deploy production --force
  only:
    - staging
