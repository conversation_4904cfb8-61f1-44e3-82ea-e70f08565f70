const request = require('supertest');
const { app } = require('../app/app');

const { dbUp, dbDown } = require('./services/migrationsHelper');
const { itemsSeeder } = require('./services/itemsSeeder');
const { ordersSeeder } = require('./services/ordersSeeder');
const { customersSeeder } = require('./services/customersSeeder');
const { adminsSeeder } = require('./services/adminsSeeder');
const LoginService = require('../app/modules/auth/services/login-service');
const OrderService = require('../app/services/order-service');
const moment = require('moment');

let order1 = {};
let order2 = null;
let order3 = null;
let accessToken = '';

beforeEach(async () => {
  await dbDown();
  await dbUp();

  await itemsSeeder();
  await customersSeeder();
  await ordersSeeder();
  await adminsSeeder();

  await OrderService.getById('fd709638-ec01-9044-86f3-447610db93b6').then((data) => {
    order1 = {
      ...data.dataValues,
      orderItems: data.dataValues.orderItems.map((orderItem) => {
        return {
          ...orderItem.toJSON(),
        };
      }),
    };
  });
  await OrderService.getById('15339638-2001-9044-86f3-447ce12b9380').then((data) => {
    order2 = {
      ...data.dataValues,
      orderItems: data.dataValues.orderItems.map((orderItem) => {
        return {
          ...orderItem.toJSON(),
        };
      }),
    };
  });
  await OrderService.getById('23eb9644-20ea-9044-8900-447ce12b23ba').then((data) => {
    order3 = {
      ...data.dataValues,
      orderItems: data.dataValues.orderItems.map((orderItem) => {
        return {
          ...orderItem.toJSON(),
        };
      }),
    };
  });

  await LoginService.loginByEmailAndPasswordAndType(
    '<EMAIL>',
    '123456',
    'admin'
  ).then((data) => {
    accessToken = data.token.accessToken;
  });
});

describe('PUT /api/order/fd709638-ec01-9044-86f3-447610db93b6', function () {
  it('responds with json', function (done) {
    request(app)
      .put('/api/order/fd709638-ec01-9044-86f3-447610db93b6')
      .send({
        ...order1,
        fixationDeadline: moment()
          .set({
            month: 6, // index from 0-11
            date: 25,
            hour: 10,
            minute: 0,
            second: 0,
            millisecond: 0,
          })
          .toISOString(),
        orderItems: [
          {
            ...order1.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
            ),
            itemId: '8d709637-ec01-4945-86f3-447610db95e8',
            description: 'Item Desc',
            pickUpLocationLat: '42.0093867',
            pickUpLocationLng: '21.3508590',
            pickUpLocation: 'Makedonska Prerodba, Skopje 1000, Macedonia',
            deliveryLocationLat: 41.71518294331897,
            deliveryLocationLng: 21.769258364279178,
            deliveryLocation: 'Veles 1400, Macedonia',
            state: 'optimization',
            clientComment: 'client comment',
            internalComment: 'internal comment',
            amount: '20.15',
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            orderItemTWs: [
              {
                id: '792b26a1-c089-439a-9a43-fb62bd8da531',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '86f53b6e-2b20-4692-8d0b-28ab77bd6bff',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '3ef3a5d7-3dc2-459e-a8f8-4cbc1ba9f828',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f2210a90-0b61-4fcc-b785-67217a57f09a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'feaca271-8ddf-4dca-a96e-0e9e391a3d35',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '4064ae2f-c7b4-4ae1-8408-8681614d74a8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '62e64d6d-1a79-455b-8138-b64d36602e31',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '1a912900-38c7-432a-92ce-13e303e43e8e',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '8567ebc8-ca82-4030-89a9-3d003cde3ffa',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '7cffce35-03a8-471d-bab0-e839c70106dc',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'a78e5978-d30d-4848-ba5a-ff37f657284b',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ddcee8c2-97cb-4c77-bab4-502746177715',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '08192c03-4373-485e-8cc9-e3cc7f01a39f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '29b68e46-aad0-4996-a96a-a992ff9ba6fe',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 20,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 10,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order1.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
            ],
          },
        ],
      })
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('pickUpLocationLat', '42.0093867');
        expect(response.body.data).toHaveProperty('pickUpLocationLng', '21.3508590');
        expect(response.body.data).toHaveProperty(
          'pickUpLocation',
          'Makedonska Prerodba, Skopje 1000, Macedonia'
        );
        expect(response.body.data).toHaveProperty('deliveryLocationLat', '41.71518294331897');
        expect(response.body.data).toHaveProperty('deliveryLocationLng', '21.769258364279178');
        expect(response.body.data).toHaveProperty('deliveryLocation', 'Veles 1400, Macedonia');
        expect(response.body.data).toHaveProperty('state', 'optimization');
        expect(response.body.data).toHaveProperty('clientComment', 'client comment');
        expect(response.body.data).toHaveProperty('internalComment', 'internal comment');
        expect(response.body.data).toHaveProperty('amount', '20.15');
        expect(response.body.data).toHaveProperty(
          'fixationDeadline',
          moment()
            .set({
              month: 6, // index from 0-11
              date: 25,
              hour: 10,
              minute: 0,
              second: 0,
              millisecond: 0,
            })
            .toISOString()
        );
        expect(response.body.data.orderItems).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              itemId: '8d709637-ec01-4945-86f3-447610db95e8',
              description: 'Item Desc',
              pickUpLocationLat: '42.0093867',
              pickUpLocationLng: '21.3508590',
              pickUpLocation: 'Makedonska Prerodba, Skopje 1000, Macedonia',
              deliveryLocationLat: '41.71518294331897',
              deliveryLocationLng: '21.769258364279178',
              deliveryLocation: 'Veles 1400, Macedonia',
              state: 'optimization',
              clientComment: 'client comment',
              internalComment: 'internal comment',
              amount: '20.15',
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              routeId: null,
              item: expect.objectContaining({
                name: 'Table',
                description: 'Dining table description',
                picture: 'ad sdfenfsfsdfsdfsd',
                size: 'normal',
                weight: 'middle',
                cubic: null,
                dismountingTime: 13,
                mountingTime: 16,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
          ])
        );
        done();
      });
  });
});

describe('PUT /api/order/15339638-2001-9044-86f3-447ce12b9380', function () {
  it('responds with json', function (done) {
    request(app)
      .put('/api/order/15339638-2001-9044-86f3-447ce12b9380')
      .send({
        ...order2,
        fixationDeadline: null,
        orderItems: [
          {
            ...order2.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
            ),
            itemId: '8d709637-ec01-4944-86f3-447610db95e8',
            description: 'Item Desc New',
            pickUpLocationLat: '42.132224',
            pickUpLocationLng: '21.721044',
            pickUpLocation: 'Pero Chicho 31, Kumanovo 1',
            deliveryLocationLat: 41.714204,
            deliveryLocationLng: 21.772558,
            deliveryLocation: 'Kosturska 29, Titov Veles',
            state: 'estimated',
            clientComment: '',
            internalComment: 'comment',
            amount: null,
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            orderItemTWs: [
              {
                id: '9e9dc7ef-d9ec-4a3c-84e3-96be557e56b5',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '3936330a-63c9-42a0-ada9-926cda4dd85a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ac989cda-e41b-499d-b7d6-8c758756fad4',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0b9c593c-f7b2-4390-9800-56448789e812',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '55c85822-b98b-4ba8-a923-e77bc9c448c9',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '711fc23c-0eec-425d-9925-66dd5ced3b43',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'd82f1b24-fe5b-43d6-bdc2-5f6c0681eed3',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '7809e023-c5bd-4ca7-aa86-a5e714b0eca8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '705b3fe0-504f-4b67-b640-e3a6b237bd22',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'aa8b9c75-63c0-4ccc-9e91-13472192579f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'aaf957b7-f6f7-4297-95ee-674e424f9b7f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '9c1eb884-c64a-4d25-866a-9099827a398a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'dc6514bb-5890-49fe-86ac-244088597180',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '987e9e5e-301c-4c8f-9b71-1c82a316449c',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '13722e8a-ad85-49ef-a95f-9ac7b326070f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b9bef27b-9352-4d13-9819-b01c7913b61c',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'd5295f08-76ab-4adb-957a-fb6134fdebcc',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '8402e088-c855-4a5e-b081-af631efe88e1',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4944-86f3-447610db95e8'
                ).id,
              },
            ],
          },
          {
            ...order2.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
            ),
            itemId: '8d709537-ec01-4945-86f3-447610db95e8',
            description: 'Item Desc New 1',
            pickUpLocationLat: '42.132224',
            pickUpLocationLng: '21.721049',
            pickUpLocation: 'Pero Chicho 31, Kumanovo 1',
            deliveryLocationLat: 42.009368,
            deliveryLocationLng: 21.350275,
            deliveryLocation: 'Makedonska Prerodba, Skopje',
            state: 'confirmed',
            clientComment: '',
            internalComment: '',
            amount: '15.63',
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            orderItemTWs: [
              {
                id: '9e9dc7ef-d9ec-4a3c-84e3-96be557e56b5',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 10,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '3936330a-63c9-42a0-ada9-926cda4dd85a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 11,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ac989cda-e41b-499d-b7d6-8c758756fad4',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 12,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0b9c593c-f7b2-4390-9800-56448789e812',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 13,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '55c85822-b98b-4ba8-a923-e77bc9c448c9',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 14,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '711fc23c-0eec-425d-9925-66dd5ced3b43',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 15,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'd82f1b24-fe5b-43d6-bdc2-5f6c0681eed3',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 16,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '7809e023-c5bd-4ca7-aa86-a5e714b0eca8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 17,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '705b3fe0-504f-4b67-b640-e3a6b237bd22',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'aa8b9c75-63c0-4ccc-9e91-13472192579f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'aaf957b7-f6f7-4297-95ee-674e424f9b7f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 18,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 12,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '9c1eb884-c64a-4d25-866a-9099827a398a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 2,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'dc6514bb-5890-49fe-86ac-244088597180',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 3,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '987e9e5e-301c-4c8f-9b71-1c82a316449c',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 4,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '13722e8a-ad85-49ef-a95f-9ac7b326070f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 5,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b9bef27b-9352-4d13-9819-b01c7913b61c',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 6,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'd5295f08-76ab-4adb-957a-fb6134fdebcc',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 7,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '8402e088-c855-4a5e-b081-af631efe88e1',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 8,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order2.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709537-ec01-4945-86f3-447610db95e8'
                ).id,
              },
            ],
          },
        ],
      })
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('pickUpLocationLat', '42.132224');
        expect(response.body.data).toHaveProperty('pickUpLocationLng', '21.721044');
        expect(response.body.data).toHaveProperty('pickUpLocation', 'Pero Chicho 31, Kumanovo 1');
        expect(response.body.data).toHaveProperty('deliveryLocationLat', '41.714204');
        expect(response.body.data).toHaveProperty('deliveryLocationLng', '21.772558');
        expect(response.body.data).toHaveProperty('deliveryLocation', 'Kosturska 29, Titov Veles');
        expect(response.body.data).toHaveProperty('state', 'estimated');
        expect(response.body.data).toHaveProperty('clientComment', '');
        expect(response.body.data).toHaveProperty('internalComment', 'comment');
        expect(response.body.data).toHaveProperty('amount', null);
        expect(response.body.data).toHaveProperty('fixationDeadline', null);
        expect(response.body.data.orderItems).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              itemId: '8d709637-ec01-4944-86f3-447610db95e8',
              description: 'Item Desc New',
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721044',
              pickUpLocation: 'Pero Chicho 31, Kumanovo 1',
              deliveryLocationLat: '41.714204',
              deliveryLocationLng: '21.772558',
              deliveryLocation: 'Kosturska 29, Titov Veles',
              state: 'estimated',
              clientComment: '',
              internalComment: 'comment',
              amount: null,
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              routeId: null,
              item: expect.objectContaining({
                name: 'Sofa',
                description: 'sofa description',
                picture: 'enfsfsdfsdfsd',
                size: 'large',
                weight: 'middle',
                cubic: null,
                dismountingTime: 20,
                mountingTime: 25,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
            expect.objectContaining({
              itemId: '8d709537-ec01-4945-86f3-447610db95e8',
              description: 'Item Desc New 1',
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721049',
              pickUpLocation: 'Pero Chicho 31, Kumanovo 1',
              deliveryLocationLat: '42.009368',
              deliveryLocationLng: '21.350275',
              deliveryLocation: 'Makedonska Prerodba, Skopje',
              state: 'confirmed',
              clientComment: '',
              internalComment: '',
              amount: '15.63',
              routeId: null,
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              item: expect.objectContaining({
                name: 'TV',
                description: 'TV desc',
                picture: 'ad sdfenfsfsdfsdfsd',
                size: 'normal',
                weight: 'middle',
                cubic: null,
                dismountingTime: 15,
                mountingTime: 20,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 13,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 14,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 15,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 16,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 17,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 18,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 12,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 2,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 3,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 4,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 5,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 6,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 7,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 8,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
          ])
        );
        done();
      });
  });
});

describe('PUT /api/order/23eb9644-20ea-9044-8900-447ce12b23ba', function () {
  it('responds with json', function (done) {
    request(app)
      .put('/api/order/23eb9644-20ea-9044-8900-447ce12b23ba')
      .send({
        fixationDeadline: moment()
          .set({
            month: 6, // index from 0-11
            date: 29,
            hour: 10,
            minute: 0,
            second: 0,
            millisecond: 0,
          })
          .toISOString(),
        orderItems: [
          {
            ...order3.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
            ),
            itemId: '8d709638-ec01-4944-86f3-447610db95e8',
            description: '',
            pickUpLocationLat: 41.715579,
            pickUpLocationLng: 21.770344,
            pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
            deliveryLocationLat: 41.344396,
            deliveryLocationLng: 21.549146,
            deliveryLocation: 'Orde Kabeco, Prilep',
            state: 'estimated',
            clientComment: '',
            internalComment: '',
            amount: null,
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            orderItemTWs: [
              {
                id: 'af0b29e7-cae4-4baf-90a0-8267160a423e',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '90360003-985d-4367-a9ad-ee776479fc5a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '1e4d7958-66fa-48ae-87ec-f015c45d8e8e',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '52588588-5590-4f16-add4-3f8124ba98d4',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '6a9f870a-2541-4df8-bdf4-8a0160366169',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '158d95de-9a13-4f56-8a81-4e725a0ca16a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '58b17878-57bb-430f-9bc2-f9e4793925b3',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '47218f0d-e8cc-44e2-85d4-5c1ca8b83812',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '485f7f38-1a73-43e1-a88a-03c6cde9476f',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                // Update time farem by id
                id: order3.orderItems
                  .find((orderItem) => orderItem.itemId == '8d709638-ec01-4944-86f3-447610db95e8')
                  .orderItemTWs.find(
                    (orderItemTW) =>
                      moment(orderItemTW.earliestPickUp).toISOString() ==
                      moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString()
                  ).id,
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '4335743a-60dd-4ef6-a4f7-70cdaf79d7bb',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b934a1ce-8881-4d8b-a7ba-4940a19b1cc8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '01e2003c-2ba0-459a-b36d-d4deeb9b7aeb',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '6bb618d6-f799-4476-8822-b5ac66487db8',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '45e41854-8bff-46a9-ac14-0bd238fab458',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0158bfd4-59fd-4893-9aed-e15b1157369b',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '613ba36a-23e1-40c9-8ff5-a52f07fdccbf',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '7e328125-72df-403a-962e-3fdd7f778042',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '961c4db5-568a-4237-b5e1-e531eed49d7d',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '438bb991-f81f-4a37-946a-e230f8e125fc',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              //
              {
                id: '08a8687a-bd9e-4697-b33a-6786973e0cb4',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
            ],
          },
          {
            ...order3.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
            ),
            itemId: '8d709637-ec01-4945-86f3-447610db95e8',
            description: '',
            pickUpLocationLat: 41.715579,
            pickUpLocationLng: 21.770344,
            pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
            deliveryLocationLat: 41.344396,
            deliveryLocationLng: 21.549146,
            deliveryLocation: 'Orde Kabeco, Prilep',
            state: 'estimated',
            clientComment: '',
            internalComment: '',
            amount: null,
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            orderItemTWs: [
              {
                id: 'acc568d2-2042-4e9c-bc28-c7056fa8df87',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '9b238c7d-f114-4fc7-91ba-b607e409b699',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '6ef17754-43f2-4e3e-aad3-ffca709eef7b',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0d2bd38e-616d-4ec0-a245-5fe7586d1038',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f100356b-f5db-44f2-bc6a-9668da9fe914',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'a8dd9ae2-247e-47e5-9b2b-9025249581e5',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b21abadc-54e8-4e7e-af49-9a3f025e79e8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ce509ce5-0bd9-4a00-9425-4efce2839bd5',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '66bb1244-ed3f-437d-a534-aed05adbfff1',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                // Update time farem by id
                id: order3.orderItems
                  .find((orderItem) => orderItem.itemId == '8d709637-ec01-4945-86f3-447610db95e8')
                  .orderItemTWs.find(
                    (orderItemTW) =>
                      moment(orderItemTW.earliestPickUp).toISOString() ===
                      moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString()
                  ).id,
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'c3251000-9b55-448c-96c1-6c56d685970a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '93a7f6f9-ca1c-43c0-ab2f-7c4334d47d5d',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '811feafb-296f-4b61-aed4-0b83584a7950',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f7667010-bebe-4e91-a068-d4f28d05b600',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '116edb5c-5fe0-4d00-976a-5ca0c8f87b0d',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0f6220dd-c8d3-43e8-8bb2-7eb90d5c28ad',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ed974b70-2e91-48ac-aea8-6b209806392f',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '1160db75-00f7-4362-a74b-0b0b5885aab4',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'c7b6d89c-e6cb-4fae-89fc-e434ed2a0d60',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              {
                id: '0183dd26-9d7b-4df6-baf5-1650df87a566',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
              //
              {
                id: 'da197416-dde0-4bf4-aacb-87e03a4f3bfc',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
                ).id,
              },
            ],
          },
          {
            ...order3.orderItems.find(
              (orderItem) => orderItem.itemId === '8d709637-ec01-4945-86f3-447610db95e8'
            ),
            id: '61079501-2337-49ad-8ff0-d36a0afdc4ad',
            itemId: '8d709537-ec01-4945-86f3-447610db95e8',
            description: '',
            pickUpLocationLat: 41.715577,
            pickUpLocationLng: 21.770347,
            pickUpLocation: 'Благој Ѓорев 99-95, T Veles',
            deliveryLocationLat: 41.344399,
            deliveryLocationLng: 21.549149,
            deliveryLocation: 'Orde Kabeco, Prilep 1',
            state: 'optimization',
            clientComment: 'cc',
            internalComment: 'ic',
            amount: '20.00',
            routeId: null,
            expectedArrivalDateTime: null,
            arrivalDateTime: null,
            // item: {
            //   // carryingTime: null,
            //   // createdAt: '2021-07-30T11:16:47.953Z',
            //   // cubic: null,
            //   // id: '8d709638-ec01-4944-86f3-447610db95e8',
            //   // updatedAt: '2021-08-25T08:55:39.221Z',

            //   //
            //   name: 'Other',
            //   description: '',
            //   picture: 'ad sdfenfsfsdfsdfsd',
            //   size: 'normal',
            //   weight: 'middle',
            //   dismountingTime: 15,
            //   mountingTime: 20,
            // },
            orderItemTWs: [
              {
                id: '2d70980f-3832-445f-8c5f-c82d138e85bd',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 18,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f1e65b6f-951e-42fc-9818-bfcb9a5e8c4e',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 19,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'ab2c3bce-307c-4178-9937-3dff591f4bbe',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 20,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '00c2dd40-a21a-4948-9301-ce58840a4b00',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 21,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '98bf5792-820e-47ac-8648-14472d547787',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 22,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'fb31c927-df71-4895-868f-a7989f33436a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 23,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'aa4669f0-cb6e-4b58-948e-a2a14f331690',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 24,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '78f5881f-125e-4785-8808-a25589da48a2',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 25,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '76bc8ce5-bbff-4ea8-8aa2-3c0b9afe0c0a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 26,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'eee8973f-12ba-4721-8f59-6fa9526009e1',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 27,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '8e622b08-badb-45e3-86ca-6e2dec1813b8',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 28,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f5101b1c-0ac3-476e-8e06-efb996108653',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 29,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '271a96d4-4eef-4fb1-b4c1-f2ba286fe37a',
                earliestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 20,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                    hour: 9,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 6, // index from 0-11
                    date: 30,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'f50c2b55-4696-425a-9de6-da2471e4cead',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 5,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'de3b7198-461d-4079-9250-5bfb442875c0',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 6,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '6ecc1c44-e4fb-4ce7-ab96-a1b640ba744b',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 7,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '9fb65558-5f97-4e58-acf1-41f48c103785',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 8,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'd801faa5-3c33-4d7e-a3ce-b25b6aa45a8f',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 9,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b7af2ee8-d624-4c20-a102-2788c59a72ec',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 10,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: '41fea466-aa04-4455-b0fb-377ad2634e68',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 11,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
              {
                id: 'b67a5561-f976-4086-93c2-a40b4f220742',
                earliestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestPickUp: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 17,
                    minute: 0,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                earliestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                    hour: 11,
                    minute: 30,
                    second: 0,
                    millisecond: 0,
                  })
                  .toISOString(),
                latestDelivery: moment()
                  .set({
                    month: 7, // index from 0-11
                    date: 12,
                  })
                  .endOf('day')
                  .toISOString(),
                orderItemId: order3.orderItems.find(
                  (orderItem) => orderItem.itemId === '8d709638-ec01-4944-86f3-447610db95e8'
                ).id,
              },
            ],
          },
        ],
      })
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('pickUpLocationLat', '41.715579');
        expect(response.body.data).toHaveProperty('pickUpLocationLng', '21.770344');
        expect(response.body.data).toHaveProperty(
          'pickUpLocation',
          'Благој Ѓорев 99-95, Veles 1400'
        );
        expect(response.body.data).toHaveProperty('deliveryLocationLat', '41.344396');
        expect(response.body.data).toHaveProperty('deliveryLocationLng', '21.549146');
        expect(response.body.data).toHaveProperty('deliveryLocation', 'Orde Kabeco, Prilep');
        expect(response.body.data).toHaveProperty('state', 'estimated');
        expect(response.body.data).toHaveProperty('clientComment', '');
        expect(response.body.data).toHaveProperty('internalComment', '');
        expect(response.body.data).toHaveProperty('amount', null);
        expect(response.body.data).toHaveProperty(
          'fixationDeadline',
          moment()
            .set({
              month: 6, // index from 0-11
              date: 29,
              hour: 10,
              minute: 0,
              second: 0,
              millisecond: 0,
            })
            .toISOString()
        );
        expect(response.body.data.orderItems).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              itemId: '8d709638-ec01-4944-86f3-447610db95e8',
              description: '',
              pickUpLocationLat: '41.715579',
              pickUpLocationLng: '21.770344',
              pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'estimated',
              clientComment: '',
              internalComment: '',
              amount: null,
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              routeId: null,
              item: expect.objectContaining({
                name: 'Wardrobe',
                description: 'Wardrobe description',
                picture: 'enfsfsdfsdfsd sdfd',
                size: 'large',
                weight: 'heavy',
                cubic: null,
                dismountingTime: 1,
                mountingTime: 1,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
            expect.objectContaining({
              itemId: '8d709637-ec01-4945-86f3-447610db95e8',
              description: '',
              pickUpLocationLat: '41.715579',
              pickUpLocationLng: '21.770344',
              pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'estimated',
              clientComment: '',
              internalComment: '',
              amount: null,
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              routeId: null,
              item: expect.objectContaining({
                name: 'Table',
                description: 'Dining table description',
                picture: 'ad sdfenfsfsdfsdfsd',
                size: 'normal',
                weight: 'middle',
                cubic: null,
                dismountingTime: 13,
                mountingTime: 16,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
            expect.objectContaining({
              itemId: '8d709537-ec01-4945-86f3-447610db95e8',
              pickUpLocationLat: '41.715577',
              pickUpLocationLng: '21.770347',
              pickUpLocation: 'Благој Ѓорев 99-95, T Veles',
              deliveryLocationLat: '41.344399',
              deliveryLocationLng: '21.549149',
              deliveryLocation: 'Orde Kabeco, Prilep 1',
              state: 'optimization',
              clientComment: 'cc',
              internalComment: 'ic',
              amount: '20.00',
              routeId: null,
              expectedArrivalDateTime: null,
              arrivalDateTime: null,
              routeId: null,
              item: expect.objectContaining({
                name: 'TV',
                description: 'TV desc',
                picture: 'ad sdfenfsfsdfsdfsd',
                size: 'normal',
                weight: 'middle',
                dismountingTime: 15,
                mountingTime: 20,
                cubic: null,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 21,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 22,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 23,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 24,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 25,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 26,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 27,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 28,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 29,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 20,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                      hour: 9,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 6, // index from 0-11
                      date: 30,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 5,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 6,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 7,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 8,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 9,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 17,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                      hour: 11,
                      minute: 30,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 7, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
          ])
        );
        done();
      });
  });
});
