const request = require('supertest');
const { app } = require('../app/app');

const { dbUp, dbDown } = require('./services/migrationsHelper');
const { itemsSeeder } = require('./services/itemsSeeder');
const moment = require('moment');

beforeEach(async () => {
  await dbDown();
  await dbUp();

  await itemsSeeder();
});

describe('POST /api/order', function () {
  it('responds with json', function (done) {
    request(app)
      .post('/api/order')
      .send({
        state: 'draft',
        clientComment: '',
        internalComment: '',
        amount: null,
        description: 'desc',
        items: [
          {
            id: '8d709638-ec01-4944-86f3-447610db95e8',
            pickUpAddress:
              'bul. <PERSON><PERSON><PERSON><PERSON>, Partizanski <PERSON>dredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
            pickUpLocation: {
              lat: 42.001614615866835,
              lng: 21.409515139981078,
            },
            deliveryAddress: 'Veles 1400, North Macedonia',
            deliveryLocation: {
              lat: 41.71518294331899,
              lng: 21.769258364279178,
            },
          },
          {
            id: '8d709637-ec01-4945-86f3-447610db95e8',
            pickUpAddress: 'Makedonska Prerodba, Skopje 1000, North Macedonia',
            pickUpLocation: {
              lat: 42.0093867,
              lng: 21.3508591,
            },
            deliveryAddress: 'Veles 1400, North Macedonia',
            deliveryLocation: {
              lat: 41.71518294331899,
              lng: 21.769258364279178,
            },
          },
        ],
        timeWindows: [
          {
            earliestPickUp: moment()
              .set({
                month: 9, // index from 0-11
                date: 10,
                hour: 10,
                minute: 0,
                second: 0,
                millisecond: 0,
              })
              .toISOString(),
            latestPickUp: moment()
              .set({
                month: 9, // index from 0-11
                date: 15,
                hour: 20,
                minute: 0,
                second: 0,
                millisecond: 0,
              })
              .toISOString(),
            earliestDelivery: null,
            latestDelivery: null,
          },
          {
            earliestPickUp: moment()
              .set({
                month: 9, // index from 0-11
                date: 17,
                hour: 10,
                minute: 0,
                second: 0,
                millisecond: 0,
              })
              .toISOString(),
            latestPickUp: moment()
              .set({
                month: 9, // index from 0-11
                date: 20,
                hour: 20,
                minute: 0,
                second: 0,
                millisecond: 0,
              })
              .toISOString(),
            earliestDelivery: null,
            latestDelivery: null,
          },
        ],
      })
      .set('Accept', 'application/json')
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body).toHaveProperty('success', true);
        expect(response.body.data).toHaveProperty('pickUpLocationLat', '42.001614615866835');
        expect(response.body.data).toHaveProperty('pickUpLocationLng', '21.409515139981078');
        expect(response.body.data).toHaveProperty(
          'pickUpLocation',
          'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia'
        );
        expect(response.body.data).toHaveProperty('deliveryLocationLat', '41.71518294331899');
        expect(response.body.data).toHaveProperty('deliveryLocationLng', '21.769258364279178');
        expect(response.body.data).toHaveProperty(
          'deliveryLocation',
          'Veles 1400, North Macedonia'
        );
        expect(response.body.data).toHaveProperty('state', 'draft');
        expect(response.body.data).toHaveProperty('clientComment', '');
        expect(response.body.data).toHaveProperty('internalComment', '');
        expect(response.body.data).toHaveProperty('amount', null);
        expect(response.body.data.orderItems).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              itemId: '8d709638-ec01-4944-86f3-447610db95e8',
              description: 'desc',
              pickUpLocationLat: '42.001614615866835',
              pickUpLocationLng: '21.409515139981078',
              pickUpLocation:
                'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
              deliveryLocationLat: '41.71518294331899',
              deliveryLocationLng: '21.769258364279178',
              deliveryLocation: 'Veles 1400, North Macedonia',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              item: expect.objectContaining({
                name: 'Wardrobe',
                description: 'Wardrobe description',
                picture: 'enfsfsdfsdfsd sdfd',
                size: 'large',
                weight: 'heavy',
                cubic: null,
                dismountingTime: 1,
                mountingTime: 1,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
            expect.objectContaining({
              itemId: '8d709637-ec01-4945-86f3-447610db95e8',
              description: 'desc',
              pickUpLocationLat: '42.0093867',
              pickUpLocationLng: '21.3508591',
              pickUpLocation: 'Makedonska Prerodba, Skopje 1000, North Macedonia',
              deliveryLocationLat: '41.71518294331899',
              deliveryLocationLng: '21.769258364279178',
              deliveryLocation: 'Veles 1400, North Macedonia',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              item: expect.objectContaining({
                name: 'Table',
                description: 'Dining table description',
                picture: 'ad sdfenfsfsdfsdfsd',
                size: 'normal',
                weight: 'middle',
                cubic: null,
                dismountingTime: 13,
                mountingTime: 16,
              }),
              orderItemTWs: expect.arrayContaining([
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 17,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 18,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 19,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 20,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 10,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 11,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 12,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 13,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 14,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
                expect.objectContaining({
                  earliestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestPickUp: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 20,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  earliestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                      hour: 10,
                      minute: 0,
                      second: 0,
                      millisecond: 0,
                    })
                    .toISOString(),
                  latestDelivery: moment()
                    .set({
                      month: 9, // index from 0-11
                      date: 15,
                    })
                    .endOf('day')
                    .toISOString(),
                }),
              ]),
            }),
          ])
        );
        done();
      });
  });
});
