const request = require('supertest');
const { app } = require('../app/app');

const { dbUp, dbDown } = require('./services/migrationsHelper');
const { ordersSeeder } = require('./services/ordersSeeder');
const { customersSeeder } = require('./services/customersSeeder');
const { itemsSeeder } = require('./services/itemsSeeder');
const { adminsSeeder } = require('./services/adminsSeeder');
const LoginService = require('../app/modules/auth/services/login-service');
const moment = require('moment');

let accessToken = '';

beforeEach(async () => {
  await dbDown();
  await dbUp();

  await itemsSeeder();
  await customersSeeder();
  await ordersSeeder();
  await adminsSeeder();

  await LoginService.loginByEmailAndPasswordAndType(
    '<EMAIL>',
    '123456',
    'admin'
  ).then((data) => {
    accessToken = data.token.accessToken;
  });
});

describe('GET /api/orders?page=1&rowsPerPage=3', function () {
  it('responds with json', function (done) {
    request(app)
      .get('/api/orders?page=1&rowsPerPage=3')
      .send()
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body.data.result).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              pickUpLocationLat: '42.001614615866835',
              pickUpLocationLng: '21.409515139981078',
              pickUpLocation:
                'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
              deliveryLocationLat: '41.71518294331899',
              deliveryLocationLng: '21.769258364279178',
              deliveryLocation: 'Veles 1400, North Macedonia',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: null,
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc',
                  pickUpLocationLat: '42.001614615866835',
                  pickUpLocationLng: '21.409515139981078',
                  pickUpLocation:
                    'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
                  deliveryLocationLat: '41.71518294331899',
                  deliveryLocationLng: '21.769258364279178',
                  deliveryLocation: 'Veles 1400, North Macedonia',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc',
                  pickUpLocationLat: '42.0093867',
                  pickUpLocationLng: '21.3508591',
                  pickUpLocation: 'Makedonska Prerodba, Skopje 1000, North Macedonia',
                  deliveryLocationLat: '41.71518294331899',
                  deliveryLocationLng: '21.769258364279178',
                  deliveryLocation: 'Veles 1400, North Macedonia',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.001614615866835',
              pickUpLocationLng: '21.409515139981078',
              pickUpLocation:
                'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
              deliveryLocationLat: '41.71518294331899',
              deliveryLocationLng: '21.769258364279178',
              deliveryLocation: 'Veles 1400, North Macedonia',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: null,
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 1',
                  pickUpLocationLat: '42.001614615866835',
                  pickUpLocationLng: '21.409515139981078',
                  pickUpLocation:
                    'bul. Partizanski Odredi, Partizanski Odredi 87, Blvd. Partizanski Odredi 87, Skopje 1000, North Macedonia',
                  deliveryLocationLat: '41.71518294331899',
                  deliveryLocationLng: '21.769258364279178',
                  deliveryLocation: 'Veles 1400, North Macedonia',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 1',
                  pickUpLocationLat: '42.0093867',
                  pickUpLocationLng: '21.3508591',
                  pickUpLocation: 'Makedonska Prerodba, Skopje 1000, North Macedonia',
                  deliveryLocationLat: '41.71518294331899',
                  deliveryLocationLng: '21.769258364279178',
                  deliveryLocation: 'Veles 1400, North Macedonia',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 2, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721049',
              pickUpLocation: 'Pero Chicho 31, Kumanovo',
              deliveryLocationLat: '41.714206',
              deliveryLocationLng: '21.772558',
              deliveryLocation: 'Kosturska 29, Veles',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 29,
                  hour: 11,
                  minute: 30,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: '8f709637-ec01-4945-86f3-447610db9631',
              customer: expect.objectContaining({
                firstName: 'Slavko',
                lastName: 'Kazak',
                street: 'Dimce Mircev 12',
                birthdate: '1993-05-16T10:00:00.000Z',
                email: '<EMAIL>',
                mobile: '075369566',
                contactType: expect.arrayContaining(['email', 'phone']),
              }),
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 2',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
          ])
        );
        expect(response.body.data.result.length).toEqual(3);
        expect(response.body.data.pagination).toEqual(
          expect.objectContaining({
            rowsNumber: 10,
            rowsPerPage: 3,
            page: 1,
            sortBy: null,
            descending: false,
          })
        );
        done();
      });
  });
});

describe('GET /api/orders?page=2&rowsPerPage=4', function () {
  it('responds with json', function (done) {
    request(app)
      .get('/api/orders?page=2&rowsPerPage=4')
      .send()
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body.data.result).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721049',
              pickUpLocation: 'Pero Chicho 31, Kumanovo',
              deliveryLocationLat: '41.714206',
              deliveryLocationLng: '21.772558',
              deliveryLocation: 'Kosturska 29, Veles',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: null,
              customerId: 'c10b9600-9718-4c13-8c43-61585a28c03f',
              customer: expect.objectContaining({
                firstName: 'Roger',
                lastName: 'Federer',
                street: 'Bulevar 8-mi Septemvri 15/22',
                birthdate: '1981-09-15T10:00:00.000Z',
                email: '<EMAIL>',
                mobile: '3613009942',
                contactType: expect.arrayContaining(['email', 'phone', 'whatsapp']),
              }),
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 4',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709537-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 4',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '42.009368',
                  deliveryLocationLng: '21.350275',
                  deliveryLocation: 'Makedonska Prerodba, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'TV',
                    description: 'TV desc',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 15,
                    mountingTime: 20,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 13,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 14,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 17,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 8,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.009440',
              pickUpLocationLng: '21.352550',
              pickUpLocation: 'Makedonska Prerodba',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: null,
              customerId: 'a3ae4b5d-80f3-4279-b22c-67842c740c0a',
              customer: expect.objectContaining({
                firstName: 'David',
                lastName: 'Beckam',
                street: 'Franklin Ruzvelt 18',
                birthdate: '1980-08-08T10:00:00.000Z',
                email: '<EMAIL>',
                mobile: '30222349',
                contactType: expect.arrayContaining(['email']),
              }),
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 5',
                  pickUpLocationLat: '42.009440',
                  pickUpLocationLng: '21.352550',
                  pickUpLocation: 'Makedonska Prerodba',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 15,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 15,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 16,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 16,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 17,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 17,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 18,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 12,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.009440',
              pickUpLocationLng: '21.352550',
              pickUpLocation: 'Makedonska Prerodba, Skopje',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: null,
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 6',
                  pickUpLocationLat: '42.009440',
                  pickUpLocationLng: '21.352550',
                  pickUpLocation: 'Makedonska Prerodba, Skopje',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 6',
                  pickUpLocationLat: '42.009440',
                  pickUpLocationLng: '21.352550',
                  pickUpLocation: 'Makedonska Prerodba, Skopje',
                  deliveryLocationLat: '41.715579',
                  deliveryLocationLng: '21.770344',
                  deliveryLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 19,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 26,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 27,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 28,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 29,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 15,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '41.715579',
              pickUpLocationLng: '21.770344',
              pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 10,
                  hour: 0,
                  minute: 0,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 7',
                  pickUpLocationLat: '41.715579',
                  pickUpLocationLng: '21.770344',
                  pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 7',
                  pickUpLocationLat: '41.715579',
                  pickUpLocationLng: '21.770344',
                  pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
          ])
        );
        expect(response.body.data.result.length).toEqual(4);
        expect(response.body.data.pagination).toEqual(
          expect.objectContaining({
            rowsNumber: 10,
            rowsPerPage: 4,
            page: 2,
            sortBy: null,
            descending: false,
          })
        );
        done();
      });
  });
});

describe('GET /api/orders?page=1&rowsPerPage=5&sortBy=fixationDeadline&descending=false', function () {
  it('responds with json', function (done) {
    request(app)
      .get('/api/orders?page=1&rowsPerPage=5&sortBy=fixationDeadline&descending=false')
      .send()
      .set({ Accept: 'application/json', Authorization: accessToken })
      .expect('Content-Type', /json/)
      .expect(200)
      .then((response) => {
        expect(response.body.data.result).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              pickUpLocationLat: '42.001423',
              pickUpLocationLng: '21.406983',
              pickUpLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
              deliveryLocationLat: '42.000396',
              deliveryLocationLng: '21.424449',
              deliveryLocation: 'Leninova, Skopje',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 29,
                  hour: 0,
                  minute: 0,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: 'a3ae4b5d-80f3-4279-b22c-67842c740c0a',
              customer: expect.objectContaining({
                firstName: 'David',
                lastName: 'Beckam',
                street: 'Franklin Ruzvelt 18',
                birthdate: '1980-08-08T10:00:00.000Z',
                email: '<EMAIL>',
                mobile: '30222349',
                contactType: expect.arrayContaining(['email']),
              }),
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 9',
                  pickUpLocationLat: '42.001423',
                  pickUpLocationLng: '21.406983',
                  pickUpLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
                  deliveryLocationLat: '42.000396',
                  deliveryLocationLng: '21.424449',
                  deliveryLocation: 'Leninova, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 9',
                  pickUpLocationLat: '42.001423',
                  pickUpLocationLng: '21.406983',
                  pickUpLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
                  deliveryLocationLat: '42.000396',
                  deliveryLocationLng: '21.424449',
                  deliveryLocation: 'Leninova, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 9',
                  pickUpLocationLat: '42.001423',
                  pickUpLocationLng: '21.406983',
                  pickUpLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
                  deliveryLocationLat: '42.000396',
                  deliveryLocationLng: '21.424449',
                  deliveryLocation: 'Leninova, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709537-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 9',
                  pickUpLocationLat: '42.001423',
                  pickUpLocationLng: '21.406983',
                  pickUpLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
                  deliveryLocationLat: '42.000396',
                  deliveryLocationLng: '21.424449',
                  deliveryLocation: 'Leninova, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'TV',
                    description: 'TV desc',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 15,
                    mountingTime: 20,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 13,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 10,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 11,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 12,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 12,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 15,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 16,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 17,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 19,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                          hour: 10,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '41.715579',
              pickUpLocationLng: '21.770344',
              pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
              deliveryLocationLat: '41.344396',
              deliveryLocationLng: '21.549146',
              deliveryLocation: 'Orde Kabeco, Prilep',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 10,
                  hour: 0,
                  minute: 0,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 7',
                  pickUpLocationLat: '41.715579',
                  pickUpLocationLng: '21.770344',
                  pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709637-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 7',
                  pickUpLocationLat: '41.715579',
                  pickUpLocationLng: '21.770344',
                  pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  deliveryLocationLat: '41.344396',
                  deliveryLocationLng: '21.549146',
                  deliveryLocation: 'Orde Kabeco, Prilep',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Table',
                    description: 'Dining table description',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 13,
                    mountingTime: 16,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 18,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 19,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 20,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 21,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 22,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 23,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 24,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 20,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 25,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721049',
              pickUpLocation: 'Pero Chicho 31, Kumanovo',
              deliveryLocationLat: '41.714206',
              deliveryLocationLng: '21.772558',
              deliveryLocation: 'Kosturska 29, Veles',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 29,
                  hour: 11,
                  minute: 30,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 3',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 3',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
                expect.objectContaining({
                  itemId: '8d709537-ec01-4945-86f3-447610db95e8',
                  description: 'Order Desc 3',
                  pickUpLocationLat: '42.133604',
                  pickUpLocationLng: '21.719346',
                  pickUpLocation: 'Narodna Revolucija 1, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'TV',
                    description: 'TV desc',
                    picture: 'ad sdfenfsfsdfsdfsd',
                    size: 'normal',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 15,
                    mountingTime: 20,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '41.715579',
              pickUpLocationLng: '21.770344',
              pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
              deliveryLocationLat: '42.001423',
              deliveryLocationLng: '21.406983',
              deliveryLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 30,
                  hour: 9,
                  minute: 0,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: null,
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709638-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 8',
                  pickUpLocationLat: '41.715579',
                  pickUpLocationLng: '21.770344',
                  pickUpLocation: 'Благој Ѓорев 99-95, Veles 1400',
                  deliveryLocationLat: '42.001423',
                  deliveryLocationLng: '21.406983',
                  deliveryLocation: 'Partizanski Odredi 87, Boulevard Partizanski Odredi, Skopje',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Wardrobe',
                    description: 'Wardrobe description',
                    picture: 'enfsfsdfsdfsd sdfd',
                    size: 'large',
                    weight: 'heavy',
                    cubic: null,
                    dismountingTime: 1,
                    mountingTime: 1,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 15,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 9,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
            expect.objectContaining({
              pickUpLocationLat: '42.132224',
              pickUpLocationLng: '21.721049',
              pickUpLocation: 'Pero Chicho 31, Kumanovo',
              deliveryLocationLat: '41.714206',
              deliveryLocationLng: '21.772558',
              deliveryLocation: 'Kosturska 29, Veles',
              state: 'draft',
              clientComment: '',
              internalComment: '',
              amount: null,
              fixationDeadline: moment()
                .set({
                  month: 5, // index from 0-11
                  date: 29,
                  hour: 11,
                  minute: 30,
                  second: 0,
                  millisecond: 0,
                })
                .toISOString(),
              customerId: '8f709637-ec01-4945-86f3-447610db9631',
              customer: expect.objectContaining({
                firstName: 'Slavko',
                lastName: 'Kazak',
                street: 'Dimce Mircev 12',
                birthdate: '1993-05-16T10:00:00.000Z',
                email: '<EMAIL>',
                mobile: '075369566',
                contactType: expect.arrayContaining(['email', 'phone']),
              }),
              orderItems: expect.arrayContaining([
                expect.objectContaining({
                  itemId: '8d709637-ec01-4944-86f3-447610db95e8',
                  description: 'Order Desc 2',
                  pickUpLocationLat: '42.132224',
                  pickUpLocationLng: '21.721049',
                  pickUpLocation: 'Pero Chicho 31, Kumanovo',
                  deliveryLocationLat: '41.714206',
                  deliveryLocationLng: '21.772558',
                  deliveryLocation: 'Kosturska 29, Veles',
                  state: 'draft',
                  clientComment: '',
                  internalComment: '',
                  amount: null,
                  routeId: null,
                  expectedArrivalDateTime: null,
                  arrivalDateTime: null,
                  item: expect.objectContaining({
                    name: 'Sofa',
                    description: 'sofa description',
                    picture: 'enfsfsdfsdfsd',
                    size: 'large',
                    weight: 'middle',
                    cubic: null,
                    dismountingTime: 20,
                    mountingTime: 25,
                  }),
                  orderItemTWs: expect.arrayContaining([
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 5, // index from 0-11
                          date: 30,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 1,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 2,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 3,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 4,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 5,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 6,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                    expect.objectContaining({
                      earliestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestPickUp: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 14,
                          minute: 0,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      earliestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                          hour: 11,
                          minute: 30,
                          second: 0,
                          millisecond: 0,
                        })
                        .toISOString(),
                      latestDelivery: moment()
                        .set({
                          month: 6, // index from 0-11
                          date: 7,
                        })
                        .endOf('day')
                        .toISOString(),
                    }),
                  ]),
                }),
              ]),
            }),
          ])
        );
        expect(response.body.data.result.length).toEqual(5);
        expect(response.body.data.pagination).toEqual(
          expect.objectContaining({
            rowsNumber: 10,
            rowsPerPage: 5,
            page: 1,
            sortBy: 'fixationDeadline',
            descending: false,
          })
        );
        done();
      });
  });
});

describe('GET /api/orders?page=2&rowsPerPage=4', function () {
  it('responds with json', function (done) {
    request(app)
      .get('/api/orders?page=2&rowsPerPage=4')
      .send()
      .set({ Accept: 'application/json' })
      .expect('Content-Type', /json/)
      .expect(401)
      .then((response) => {
        done();
      });
  });
});
