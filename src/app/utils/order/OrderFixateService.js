const { LocationClient, CalculateRouteCommand } = require('@aws-sdk/client-location');
const { fromCognitoIdentityPool } = require('@aws-sdk/credential-providers');
const moment = require('moment');
const RouteService = require('../../services/route-service');
const OrderItemService = require('../../services/OrderItemService');
const MoverService = require('../../services/mover-service');
const OrderRepository = require('../../modules/repositories/order-repository');
const ConfigService = require('../../services/config-service');
const { routeTryWindowService } = require('../../services/route/RouteTryWindowService');
const { sleep } = require('../../../lib/utils');

const AWS_SERVICE_REGION = process.env.AWS_LOCATION_SERVICE_REGION || 'eu-central-1';
const AWS_SERVICE_IDENTITY_POOL_ID =
  process.env.AWS_LOCATION_SERVICE_IDENTITY_POOL_ID ||
  'eu-central-1:3cea33a3-dd77-4148-b113-6847fc1cfafd';

class OrderFixateService {
  constructor() {
    this.awsConfig();
  }
  awsConfig = () => {
    AWS.config.region = AWS_SERVICE_REGION;
    AWS.config.credentials = new AWS.CognitoIdentityCredentials({
      IdentityPoolId: AWS_SERVICE_IDENTITY_POOL_ID,
    });
  };

  calculateFixationDeadline = async (confirmationDate) => {
    if (!confirmationDate) {
      return null;
    }

    const config = await ConfigService.getByKey('hoursOffsetForFixation');

    const offsetForFixationValue = config?.value;

    if (!offsetForFixationValue) {
      return confirmationDate;
    }

    return moment(confirmationDate)
      .add('hours', offsetForFixationValue)
      .format('YYYY-MM-DD HH:mm:ssZ');
  };

  fixateOrderByRoutes = async (route, smartXRequestId) => {
    return await this.processAllRoutes(route, smartXRequestId);
  };

  processAllRoutes = async (route, smartXRequestId) => {
    try {
      const transportationDate = route.stations?.[0].arrival_time;

      const bankpaymentfee = Number(
        await ConfigService.getByKeyOrDefaultValue('bankpaymentfee', 6)
      );

      const dbRoute = await RouteService.create({
        path: route.stations,
        depotIndex: route.vehicle.depot_index,
        price: Number(route.cost) - bankpaymentfee,
        transportationDate,
        smartXRequestId,
        bankpaymentfee,
      });

      if (!dbRoute) return;

      const { stations } = route;
      for (const station of stations) {
        if (!station.item_id) continue;
        OrderItemService.updateById(
          {
            routeId: dbRoute.id,
            expectedArrivalDateTime: station.arrival_time,
            // status: ORDER_STATUS.MATCHING,
          },
          station.item_id
        )
          .then(async (orderItem) => {
            dbRoute.orderId = orderItem.orderId;
            await dbRoute.save();
            await OrderRepository.updateById(
              {
                // status: ORDER_STATUS.MATCHING,
                transportationDate,
              },
              orderItem.orderId
            );
          })
          .catch((err) => console.log(err));
      }

      return dbRoute;
    } catch (e) {
      console.error(e);
    }
  };

  async processOrderFixationData(routes, smartXRequestId = null) {
    console.log('Processing order fixation data');
    await routes.reduce(async (acc, route) => {
      const savedRoute = await this.fixateOrderByRoutes(route, smartXRequestId);
      if (!savedRoute) return acc;

      await routeTryWindowService.createTryWindows(savedRoute.id, route);

      return acc;
    }, Promise.resolve());

    return routes;
  }

  async calculateMoverDistances(route, movers) {
    const distanceMap = {};
    const startingLocation = route?.vehicle;

    if(!startingLocation || movers.length === 0) {
      return [];
    }

    const location = new AWS.Location();

    for (let i = 0; i < movers.length; i++) {
      const mover = movers[i];

      try {
        const response = await location
          .calculateRoute({
            CalculatorName: 'TixpiDistanceCalculator',
            DestinationPosition: [
              startingLocation.depot.position.longitude,
              startingLocation.depot.position.latitude,
            ],
            DeparturePosition: [
              mover.position.longitude,
              mover.position.latitude,
            ],
          })
          .promise();

        distanceMap[mover.id] = {
          ...mover,
          distance: response.Summary.Distance,
        };
      } catch (e) {
        console.error('Error calculating distance for mover:', mover.id, e);
      }

      // Add a 1-second delay after every 10 movers
      if ((i + 1) % 10 === 0) {
        await sleep(1000);
      }
    }

    return Object.values(distanceMap).sort((a, b) => a.distance - b.distance);
  }
}

module.exports = new OrderFixateService();
