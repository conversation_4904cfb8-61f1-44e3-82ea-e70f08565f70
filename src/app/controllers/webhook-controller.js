import {
  CustomerRepository,
  MoverRepository,
  OrderItemRepository,
  OrderRepository,
  RouteRepository,
  TransactionRepository,
} from '../modules/repositories';
import { handleError, handleResponse } from '../utils/responseHandler';
import { sendRequestEmailToMover } from '../modules/email/actions/RouteOfferToMover';
import { sendRouteConfirmationEmailToMover } from '../modules/email/actions/MoverRouteConfirmation';
import { sendPaymentConfirmationEmailToClient } from '../modules/email/actions/ClientPaymentConfirmation';
import { sendTransportConfirmationEmailToClient } from '../modules/email/actions/ClientTransportConfirmation';
import { sendCashbackConfirmationEmailToClient } from '../modules/email/actions/ClientCashbackConfirmation';
import { createStripePayment, createStripeWebhook } from '../services/payment/StripeFactory';

import {
  sendOrderCancelationWithReimburse,
  sendRouteCancellationToMover,
  sendRoutePartialCancelationToMover,
  sendOrderCancelationWithoutReimburse,
  sendRefundToClient
} from '../modules/email/actions/OrderCancelByClientConfirmation';

import OrderService from '../services/order-service';
import SmartXRequestService from '../services/smartx-requests-service';
import { sendEmailToAnotherMoverAboutAcceptanceRoute, sendRouteOperationalCheckNotification } from '../modules/email/emails';
import { sendBatchJobFailSystemEmail } from '../modules/email/actions/BatchJobFailEmail';

const OrderFixateService = require('../utils/order/OrderFixateService');

module.exports = {
  orderFixation: async (req, res, next) => {
    console.log('Order Fixation Webhook Received');

    console.log(JSON.stringify(req.body));

    const routes = req.body?.result?.routes || [];

    if (routes.length === 0) {
      return handleResponse(res, {
        message: 'No routes received',
      });
    }

    let smartXRequest = null;
    let requestId = req.body?.request_uuid;
    if (requestId) {
      smartXRequest = await SmartXRequestService.getByRequestId(requestId);
      if (smartXRequest) {
        await SmartXRequestService.update(smartXRequest.id, {
          response: req.body,
          responseDate: new Date(),
          totalCost: req.body?.result?.total_cost || null,
        });
      }
    }

    const orderFixationProcessResult = await OrderFixateService.processOrderFixationData(
      routes,
      smartXRequest?.id
    );
    if (orderFixationProcessResult.error) {
      return handleResponse(res, {
        message: orderFixationProcessResult.error,
      });
    }

    if (smartXRequest) {
      OrderService.syncOrdersAmountAfterFixation(
        smartXRequest.orderIds,
        req.body?.result?.total_cost || 0
      );
    }

    return handleResponse(res, {
      message: 'Data Received',
    });
  },

  stripeCheckout: async (req, res) => {
    const webhook = createStripeWebhook();
    return webhook.handleEvent(req, res);
  },

  test: async (req, res) => {
    const emails = {
      moverRouteOffer: 'moverRouteOffer',
      moverRouteConfirmation: 'moverRouteConfirmation',
      sendEmailToAnotherMoverAboutAcceptanceRoute: 'sendEmailToAnotherMoverAboutAcceptanceRoute',

      clientPaymentConfirmation: 'clientPaymentConfirmation',
      clientTransportConfirmation: 'clientTransportConfirmation',
      sendCashbackConfirmationEmailToClient: 'sendCashbackConfirmationEmailToClient',

      sendOrderCancelationWithReimburse: 'sendOrderCancelationWithReimburse',
      sendOrderCancelationWithoutReimburse: 'sendOrderCancelationWithoutReimburse',
      sendRouteCancellationToMover: 'sendRouteCancellationToMover',
      sendRoutePartialCancelationToMover: 'sendRoutePartialCancelationToMover',
      sendRefundToClient: 'sendRefundToClient',

      sendBatchJobFailSystemEmail: 'sendBatchJobFailSystemEmail',
    };

    const email = req.query.email;

    let message = `Email - [${email}] Sent Successfully`;

    const [movers, routes, orders, transactions, customers] = await Promise.all([
      MoverRepository.findAll(),
      RouteRepository.findAll(),
      OrderRepository.findAll({
        include: [
          {
            association: 'customer',
          },
        ],
      }),
      TransactionRepository.findAll(),
      CustomerRepository.findAll(),
    ]);

    const orderId = 224;
    const routeId = 590;

    const route = await RouteRepository.findOneByFields({ id: routeId });
    const order = await OrderRepository.findOneById(orderId, {}, ['customer']);

    try {
      if (email === emails.moverRouteOffer) {
        await sendRequestEmailToMover(movers[0], routes[0]?.id);
      } else if (email === emails.moverRouteConfirmation) {
        await sendRouteConfirmationEmailToMover(movers[0], route);
      } else if (email === emails.sendEmailToAnotherMoverAboutAcceptanceRoute) {
        await sendEmailToAnotherMoverAboutAcceptanceRoute(movers[0], routes[0]);
      } else if (email === emails.clientPaymentConfirmation) {
        const transaction = await TransactionRepository.findOneByFields({ orderId });
        if (!order || !transaction) {
          throw new Error(`Order or transaction with id ${orderId} not found`);
        }
        await sendPaymentConfirmationEmailToClient(order, transaction);
      } else if (email === emails.clientTransportConfirmation) {

        await sendTransportConfirmationEmailToClient(movers[10], route, customers[0]);

      } else if (email === emails.sendCashbackConfirmationEmailToClient) {
        const order = await OrderService.getById(orderId);

        await sendCashbackConfirmationEmailToClient(order);
      } else if (email === emails.sendOrderCancelationWithReimburse) {
        const order = await OrderRepository.findOneById(orderId, {}, [
          'customer',
          'orderItems',
          'transactions',
        ]);
        await sendOrderCancelationWithReimburse({ order });
      } else if (email === emails.sendOrderCancelationWithoutReimburse) {
        const order = await OrderRepository.findOneById(orderId, {}, [
          'customer',
          {
            association: 'orderItems',
            include: [
              {
                association: 'item',
                include: [
                  {
                    association: 'itemCategory',
                  },
                ],
              },
            ],
          },
          'transactions',
        ]);
        await sendOrderCancelationWithoutReimburse({ order });
      } else if (email === emails.sendRouteCancellationToMover) {
        const mover = {
          mover: movers[0],
          route: routes[0],
        };
        const route = routes[0];
        
        await sendRouteCancellationToMover({
          mover,
          routeId: route.id,
          transportationDate: route.transportationDate,
        });
      } else if (email === emails.sendRoutePartialCancelationToMover) {
        const mover = {
          mover: movers[0],
          route: routes[0],
        };
        const route = routes[0];
        const rawOrderItems = await OrderItemRepository.findAllByFields({ orderId }, {}, [
          {
            association: 'item',
            include: [
              {
                association: 'itemCategory',
              },
            ],
          },
          {
            association: 'orderItemTWs',
          },
        ]);

        await sendRoutePartialCancelationToMover({
          mover,
          rawOrderItems,
          routeId: route.id,
          transportationDate: route.transportationDate,
        });
      } else if (email === emails.sendRefundToClient) {
        await sendRefundToClient(
          {
            customer: customers[0],
            amount: 100,
          }
        );
      } else if (email === emails.sendBatchJobFailSystemEmail) {
        // Sample data for sendBatchJobFailSystemEmail
        const jobName = 'Sample Batch Job';
        const jobId = 12345;
        const jobErrorMsg = 'Sample error message for batch job failure.';

        await sendBatchJobFailSystemEmail({
          email: '<EMAIL>',
          jobName,
          jobId,
          jobErrorMsg,
        });
      } else {
        message = `Email - [${email}] Not Found`;
      }

      return handleResponse(res, {
        message,
        emails: Object.values(emails),
      });
    } catch (e) {
      console.log(e);
      return handleResponse(res, handleError(500, e));
    }
  },

  testEmail: async (req, res) => {
    const movers = Array.from({ length: 30 }).map((_, i) => ({
        id: i + 1,
        email: `mover${i + 1}@example.com`,
        companyName: `Mover ${i + 1}`,
        firstName: `First${i + 1}`,
        lastName: `Last${i + 1}`,
        lang: ['en', 'de', 'fr', 'it'][i % 4], 
    }));

    const routeId = 21; // Example routeId

    try {
        const results = await Promise.all(
            movers.map(mover => sendRequestEmailToMover(mover, routeId))
        );
        console.log('All emails processed:', results.length);
    } catch (err) {
        console.error('Error in parallel send:', err);
    }

    return handleResponse(res, {
      message: 'Test email sent successfully',
    });
  },

  testStripe: async (req, res) => {
    const stripe = createStripePayment();
    const { id, url } = await stripe.checkoutSession(
      'Tixpi Order Payment',
      250,
      '<EMAIL>'
    );
    return handleResponse(res, { id, url });
  },
};