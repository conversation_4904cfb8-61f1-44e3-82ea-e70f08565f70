// SES functionality is currently disabled
// TODO: Migrate to AWS SDK v3 when SES is needed
const { env } = require('../../../../lib/utils');

const sendSESEmail = async (body, subject, recipient, sender, bcc) => {
  console.warn('SES email functionality is currently disabled. Email not sent.');
  return Promise.resolve({
    MessageId: 'disabled-ses-' + Date.now(),
  });
};

module.exports = {
  sendSESEmail,
};
