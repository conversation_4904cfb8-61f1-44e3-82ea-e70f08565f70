{"info": {"_postman_id": "8d5ab041-acb0-4342-8c49-83c23f775c7b", "name": "Tixpi Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "Webhook", "item": [{"name": "Order Fixation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"request_uuid\": \"fb087743-68c0-40fb-93db-4b1363687b1f\",\n    \"result\": {\n        \"solved\": true,\n        \"status\": \"ROUTING_SUCCESS\",\n        \"total_distance\": 833696,\n        \"total_duration\": 1108,\n        \"total_cost\": 3422,\n        \"routes\": [\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"2a88ab8a-51e7-4f4f-8cb8-4e3aa7dfd052\",\n                    \"depot\": {\n                        \"depot_id\": \"acd9b667-3d83-47c3-8d87-60dbefdf9466\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5297723,\n                            \"longitude\": 7.5974578\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 7\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 47.3924952,\n                            \"longitude\": 8.4750879\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:21:00\",\n                        \"departure_time\": \"2024-09-13 02:21:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:34:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:59:00\",\n                        \"departure_time\": \"2024-09-13 11:03:00\"\n                    }\n                ],\n                \"duration\": 547,\n                \"distance\": 391634,\n                \"cost\": 1669,\n                \"date\": \"2024-09-13\"\n            },\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"1fbf8f8c-5195-4204-b486-9c3b956d603b\",\n                    \"depot\": {\n                        \"depot_id\": \"607842dd-752b-4177-9c84-24aea2554a2e\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5773637,\n                            \"longitude\": 7.5918027\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 9\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 46.5222386,\n                            \"longitude\": 6.6303243\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:55:00\",\n                        \"departure_time\": \"2024-09-13 02:55:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:15:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:40:00\",\n                        \"departure_time\": \"2024-09-13 10:25:00\"\n                    }\n                ],\n                \"duration\": 561,\n                \"distance\": 442062,\n                \"cost\": 1753,\n                \"date\": \"2024-09-13\"\n            }\n        ],\n        \"fixed_items\": [\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\"\n        ],\n        \"items_info\": {\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\": {\n                \"name\": \"Wardrobe\",\n                \"size\": 18,\n                \"weight\": 600,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            },\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\": {\n                \"name\": \"Sofa\",\n                \"size\": 12,\n                \"weight\": 200,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"code\": 200\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/webhook/order-fixation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["webhook", "order-fixation"]}}, "response": []}, {"name": "Order Fixation [Prod]", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"request_uuid\": \"fb087743-68c0-40fb-93db-4b1363687b1f\",\n    \"result\": {\n        \"solved\": true,\n        \"status\": \"ROUTING_SUCCESS\",\n        \"total_distance\": 833696,\n        \"total_duration\": 1108,\n        \"total_cost\": 3422,\n        \"routes\": [\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"2a88ab8a-51e7-4f4f-8cb8-4e3aa7dfd052\",\n                    \"depot\": {\n                        \"depot_id\": \"acd9b667-3d83-47c3-8d87-60dbefdf9466\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5297723,\n                            \"longitude\": 7.5974578\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 7\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 47.3924952,\n                            \"longitude\": 8.4750879\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:21:00\",\n                        \"departure_time\": \"2024-09-13 02:21:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:34:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:59:00\",\n                        \"departure_time\": \"2024-09-13 11:03:00\"\n                    }\n                ],\n                \"duration\": 547,\n                \"distance\": 391634,\n                \"cost\": 1669,\n                \"date\": \"2024-09-13\"\n            },\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"1fbf8f8c-5195-4204-b486-9c3b956d603b\",\n                    \"depot\": {\n                        \"depot_id\": \"607842dd-752b-4177-9c84-24aea2554a2e\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5773637,\n                            \"longitude\": 7.5918027\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 9\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 46.5222386,\n                            \"longitude\": 6.6303243\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:55:00\",\n                        \"departure_time\": \"2024-09-13 02:55:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:15:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:40:00\",\n                        \"departure_time\": \"2024-09-13 10:25:00\"\n                    }\n                ],\n                \"duration\": 561,\n                \"distance\": 442062,\n                \"cost\": 1753,\n                \"date\": \"2024-09-13\"\n            }\n        ],\n        \"fixed_items\": [\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\"\n        ],\n        \"items_info\": {\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\": {\n                \"name\": \"Wardrobe\",\n                \"size\": 18,\n                \"weight\": 600,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            },\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\": {\n                \"name\": \"Sofa\",\n                \"size\": 12,\n                \"weight\": 200,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"code\": 200\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/webhook/order-fixation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["webhook", "order-fixation"]}}, "response": []}, {"name": "Test url", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"request_uuid\": \"fb087743-68c0-40fb-93db-4b1363687b1f\",\n    \"result\": {\n        \"solved\": true,\n        \"status\": \"ROUTING_SUCCESS\",\n        \"total_distance\": 833696,\n        \"total_duration\": 1108,\n        \"total_cost\": 3422,\n        \"routes\": [\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"2a88ab8a-51e7-4f4f-8cb8-4e3aa7dfd052\",\n                    \"depot\": {\n                        \"depot_id\": \"acd9b667-3d83-47c3-8d87-60dbefdf9466\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5297723,\n                            \"longitude\": 7.5974578\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 7\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 47.3924952,\n                            \"longitude\": 8.4750879\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:21:00\",\n                        \"departure_time\": \"2024-09-13 02:21:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:34:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:59:00\",\n                        \"departure_time\": \"2024-09-13 11:03:00\"\n                    }\n                ],\n                \"duration\": 547,\n                \"distance\": 391634,\n                \"cost\": 1669,\n                \"date\": \"2024-09-13\"\n            },\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"1fbf8f8c-5195-4204-b486-9c3b956d603b\",\n                    \"depot\": {\n                        \"depot_id\": \"607842dd-752b-4177-9c84-24aea2554a2e\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5773637,\n                            \"longitude\": 7.5918027\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 9\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 46.5222386,\n                            \"longitude\": 6.6303243\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:55:00\",\n                        \"departure_time\": \"2024-09-13 02:55:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:15:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:40:00\",\n                        \"departure_time\": \"2024-09-13 10:25:00\"\n                    }\n                ],\n                \"duration\": 561,\n                \"distance\": 442062,\n                \"cost\": 1753,\n                \"date\": \"2024-09-13\"\n            }\n        ],\n        \"fixed_items\": [\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\"\n        ],\n        \"items_info\": {\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\": {\n                \"name\": \"Wardrobe\",\n                \"size\": 18,\n                \"weight\": 600,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            },\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\": {\n                \"name\": \"Sofa\",\n                \"size\": 12,\n                \"weight\": 200,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"code\": 200\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/webhook/test", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["webhook", "test"]}}, "response": []}, {"name": "Test url [DEV]", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"request_uuid\": \"fb087743-68c0-40fb-93db-4b1363687b1f\",\n    \"result\": {\n        \"solved\": true,\n        \"status\": \"ROUTING_SUCCESS\",\n        \"total_distance\": 833696,\n        \"total_duration\": 1108,\n        \"total_cost\": 3422,\n        \"routes\": [\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"2a88ab8a-51e7-4f4f-8cb8-4e3aa7dfd052\",\n                    \"depot\": {\n                        \"depot_id\": \"acd9b667-3d83-47c3-8d87-60dbefdf9466\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5297723,\n                            \"longitude\": 7.5974578\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 7\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 47.3924952,\n                            \"longitude\": 8.4750879\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:21:00\",\n                        \"departure_time\": \"2024-09-13 02:21:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:34:00\"\n                    },\n                    {\n                        \"item_id\": \"d2349568-1602-4f07-80f5-32b6e1988d62\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:59:00\",\n                        \"departure_time\": \"2024-09-13 11:03:00\"\n                    }\n                ],\n                \"duration\": 547,\n                \"distance\": 391634,\n                \"cost\": 1669,\n                \"date\": \"2024-09-13\"\n            },\n            {\n                \"vehicle\": {\n                    \"vehicle_id\": \"1fbf8f8c-5195-4204-b486-9c3b956d603b\",\n                    \"depot\": {\n                        \"depot_id\": \"607842dd-752b-4177-9c84-24aea2554a2e\",\n                        \"num_vehicles\": 50,\n                        \"position\": {\n                            \"latitude\": 47.5773637,\n                            \"longitude\": 7.5918027\n                        },\n                        \"capacity\": {\n                            \"size\": 30,\n                            \"weight\": 1050\n                        }\n                    },\n                    \"depot_index\": 9\n                },\n                \"stations\": [\n                    {\n                        \"item_id\": null,\n                        \"position\": {\n                            \"latitude\": 46.5222386,\n                            \"longitude\": 6.6303243\n                        },\n                        \"station_type\": \"depot\",\n                        \"arrival_time\": \"2024-09-13 02:55:00\",\n                        \"departure_time\": \"2024-09-13 02:55:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.3845946,\n                            \"longitude\": 6.857083500000001\n                        },\n                        \"station_type\": \"pickup\",\n                        \"arrival_time\": \"2024-09-13 05:30:00\",\n                        \"departure_time\": \"2024-09-13 06:15:00\"\n                    },\n                    {\n                        \"item_id\": \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n                        \"position\": {\n                            \"latitude\": 46.9810074,\n                            \"longitude\": 8.6181086\n                        },\n                        \"station_type\": \"delivery\",\n                        \"arrival_time\": \"2024-09-13 09:40:00\",\n                        \"departure_time\": \"2024-09-13 10:25:00\"\n                    }\n                ],\n                \"duration\": 561,\n                \"distance\": 442062,\n                \"cost\": 1753,\n                \"date\": \"2024-09-13\"\n            }\n        ],\n        \"fixed_items\": [\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\",\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\"\n        ],\n        \"items_info\": {\n            \"d2349568-1602-4f07-80f5-32b6e1988d62\": {\n                \"name\": \"Wardrobe\",\n                \"size\": 18,\n                \"weight\": 600,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            },\n            \"0a80c37b-ebbf-4d9a-94c9-c01e9908140f\": {\n                \"name\": \"Sofa\",\n                \"size\": 12,\n                \"weight\": 200,\n                \"position\": {\n                    \"pickup\": {\n                        \"latitude\": 46.3845946,\n                        \"longitude\": 6.857083500000001\n                    },\n                    \"delivery\": {\n                        \"latitude\": 46.9810074,\n                        \"longitude\": 8.6181086\n                    }\n                },\n                \"time_windows\": {\n                    \"pickup\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ],\n                    \"delivery\": [\n                        {\n                            \"earliest\": \"2024-09-11 05:30:00\",\n                            \"latest\": \"2024-09-11 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-12 05:30:00\",\n                            \"latest\": \"2024-09-12 07:30:00\"\n                        },\n                        {\n                            \"earliest\": \"2024-09-13 05:30:00\",\n                            \"latest\": \"2024-09-13 07:30:00\"\n                        }\n                    ]\n                }\n            }\n        }\n    },\n    \"code\": 200\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/webhook/order-fixation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["webhook", "order-fixation"]}}, "response": []}]}, {"name": "Smartx Api", "item": [{"name": "Order Process", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": ";2P9(ux]ek*MJTQq", "type": "string"}, {"key": "username", "value": "API_275578_15056936", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"fixation_date\": \"all\",\n    \"price_per_hour\": 140,\n    \"price_per_km\": 1,\n    \"depots\": [\n        {\n            \"depot_id\": \"7e237369-9209-49ee-aa72-e4a2ed187dda\",\n            \"num_vehicles\": 2,\n            \"position\": {\n                \"latitude\": 47.405207,\n                \"longitude\": 8.545168\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"dba14de1-ef5e-46e5-93a3-b1d02356772c\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.403921,\n                \"longitude\": 8.512744\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"916ed876-49e7-421c-a454-6466142ad04a\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.368742,\n                \"longitude\": 8.502211\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"c8ff9812-9e1c-4713-9968-3adfb0e7d419\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.365409,\n                \"longitude\": 8.506299\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        }\n    ],\n    \"orders\": [\n        {\n            \"order_id\": \"3b4e5dfe-e13c-40df-8f04-3179032d060a\",\n            \"items\": [\n                {\n                    \"item_id\": \"b47aa062-c2a9-410c-b9e4-943563ad192a\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.402224,\n                            \"longitude\": 8.532921\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.383303,\n                            \"longitude\": 8.559411\n                        }\n                    },\n                    \"item_name\": \"Wardrobe\",\n                    \"item_size\": \"L\",\n                    \"lift\": false,\n                    \"floor\": 4,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 09:47:00\",\n                                \"latest\": \"2024-07-11 11:35:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 11:19:00\",\n                                \"latest\": \"2024-07-11 12:33:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"d2f2f80b-0161-481c-9af3-60474a3f0ed9\",\n            \"items\": [\n                {\n                    \"item_id\": \"1c6882f6-9cd0-41b2-9e4d-5c83065e6443\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.369808,\n                            \"longitude\": 8.568579\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.404902,\n                            \"longitude\": 8.50399\n                        }\n                    },\n                    \"item_name\": \"Bed\",\n                    \"item_size\": \"M\",\n                    \"lift\": false,\n                    \"floor\": 7,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 10:42:00\",\n                                \"latest\": \"2024-07-12 11:57:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 11:14:00\",\n                                \"latest\": \"2024-07-12 12:27:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"3fa63424-60ce-4ada-96a1-3c082c9c0b11\",\n            \"items\": [\n                {\n                    \"item_id\": \"243ed4ce-4a3d-4a17-8f3a-1c60916b4c9b\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.399923,\n                            \"longitude\": 8.477812\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.386932,\n                            \"longitude\": 8.503063\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"S\",\n                    \"lift\": true,\n                    \"floor\": 3,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 13:46:00\",\n                                \"latest\": \"2024-07-12 15:42:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 15:53:00\",\n                                \"latest\": \"2024-07-12 18:50:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"473fcb9c-c318-46a2-8c97-2e7038ca0568\",\n            \"items\": [\n                {\n                    \"item_id\": \"386d8a05-63f0-430e-8251-e7062519ea68\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.358907,\n                            \"longitude\": 8.498373\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.393599,\n                            \"longitude\": 8.568854\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"S\",\n                    \"lift\": true,\n                    \"floor\": 8,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 10:50:00\",\n                                \"latest\": \"2024-07-12 16:06:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 14:56:00\",\n                                \"latest\": \"2024-07-12 18:05:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"c1f0ed7b-3c10-48c4-8bda-7eb31ed19398\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.385468,\n                            \"longitude\": 8.525563\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.390954,\n                            \"longitude\": 8.478531\n                        }\n                    },\n                    \"item_name\": \"Wardrobe\",\n                    \"item_size\": \"M\",\n                    \"lift\": true,\n                    \"floor\": 7,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 15:31:00\",\n                                \"latest\": \"2024-07-11 16:53:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 16:48:00\",\n                                \"latest\": \"2024-07-11 17:59:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"bd2056e3-1cf4-48bd-9795-d67a3ac0fcca\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.407401,\n                            \"longitude\": 8.52555\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.376636,\n                            \"longitude\": 8.538191\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"S\",\n                    \"lift\": false,\n                    \"floor\": 8,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 15:14:00\",\n                                \"latest\": \"2024-07-11 16:53:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 09:27:00\",\n                                \"latest\": \"2024-07-12 11:53:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 16:59:00\",\n                                \"latest\": \"2024-07-11 18:36:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 12:05:00\",\n                                \"latest\": \"2024-07-12 15:01:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"2305212a-6998-472e-a261-0dd1c7a39d11\",\n            \"items\": [\n                {\n                    \"item_id\": \"1da92e3d-adeb-4016-b8d1-43c8e1ba4ea5\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.386024,\n                            \"longitude\": 8.519071\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.407893,\n                            \"longitude\": 8.549738\n                        }\n                    },\n                    \"item_name\": \"Sofa\",\n                    \"item_size\": \"S\",\n                    \"lift\": false,\n                    \"floor\": 6,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 15:45:00\",\n                                \"latest\": \"2024-07-11 16:53:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 16:15:00\",\n                                \"latest\": \"2024-07-11 18:04:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"c467e4d9-51b8-4337-a5ae-cecfd8698a05\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.376257,\n                            \"longitude\": 8.548716\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.388687,\n                            \"longitude\": 8.500199\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"S\",\n                    \"lift\": false,\n                    \"floor\": 6,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 12:36:00\",\n                                \"latest\": \"2024-07-11 16:15:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 13:07:00\",\n                                \"latest\": \"2024-07-12 14:14:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 16:27:00\",\n                                \"latest\": \"2024-07-11 18:45:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 14:06:00\",\n                                \"latest\": \"2024-07-12 17:48:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"4dd476d3-073c-4061-baf5-3a5a7cfa4f72\",\n            \"items\": [\n                {\n                    \"item_id\": \"e8454ac1-d315-49e8-889f-833b5ba35465\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.40468,\n                            \"longitude\": 8.54623\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.404592,\n                            \"longitude\": 8.581018\n                        }\n                    },\n                    \"item_name\": \"Wardrobe\",\n                    \"item_size\": \"L\",\n                    \"lift\": false,\n                    \"floor\": 1,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 13:44:00\",\n                                \"latest\": \"2024-07-12 15:02:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 14:24:00\",\n                                \"latest\": \"2024-07-12 15:28:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"49ad2bc0-9a7b-49e3-9bd4-715b2fe47be3\",\n            \"items\": [\n                {\n                    \"item_id\": \"d646e90f-4327-4207-9737-dcdae7c724eb\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.404605,\n                            \"longitude\": 8.5559\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.364452,\n                            \"longitude\": 8.51915\n                        }\n                    },\n                    \"item_name\": \"Bed\",\n                    \"item_size\": \"S\",\n                    \"lift\": true,\n                    \"floor\": null,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 09:25:00\",\n                                \"latest\": \"2024-07-11 11:46:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 11:00:00\",\n                                \"latest\": \"2024-07-11 16:30:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"4e452c7d-5c12-41b0-9f08-d87fde326cf9\",\n            \"items\": [\n                {\n                    \"item_id\": \"885c493c-e522-495a-8a4f-10f92afc7c07\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.384892,\n                            \"longitude\": 8.536104\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.372801,\n                            \"longitude\": 8.524672\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"L\",\n                    \"lift\": true,\n                    \"floor\": 7,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 14:47:00\",\n                                \"latest\": \"2024-07-11 16:11:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 14:13:00\",\n                                \"latest\": \"2024-07-12 16:18:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 15:50:00\",\n                                \"latest\": \"2024-07-11 17:17:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 16:48:00\",\n                                \"latest\": \"2024-07-12 18:14:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"c98c5ca3-052d-4f1c-9b5c-1364477a9e2f\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.353524,\n                            \"longitude\": 8.526082\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.361952,\n                            \"longitude\": 8.478636\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"M\",\n                    \"lift\": false,\n                    \"floor\": 3,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 14:00:00\",\n                                \"latest\": \"2024-07-12 16:04:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 15:56:00\",\n                                \"latest\": \"2024-07-12 17:50:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"7492e373-58bf-4b74-aa71-5f61f1fe276a\",\n            \"items\": [\n                {\n                    \"item_id\": \"c6d89f61-3513-4c7d-a8c7-665cdf8461bf\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.354365,\n                            \"longitude\": 8.525766\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.396584,\n                            \"longitude\": 8.549483\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"S\",\n                    \"lift\": true,\n                    \"floor\": 3,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 09:48:00\",\n                                \"latest\": \"2024-07-11 11:12:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 10:57:00\",\n                                \"latest\": \"2024-07-11 12:33:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"0190f518-7fc6-4b69-8ad3-cd45ffecbc4c\",\n            \"items\": [\n                {\n                    \"item_id\": \"b5eb443c-b22e-455d-af66-56235cc26379\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.389377,\n                            \"longitude\": 8.507083\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.389101,\n                            \"longitude\": 8.478975\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"M\",\n                    \"lift\": false,\n                    \"floor\": 6,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 15:41:00\",\n                                \"latest\": \"2024-07-12 16:59:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 17:09:00\",\n                                \"latest\": \"2024-07-12 18:19:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"d6a569ec-9113-485b-9dd9-7d68025996f9\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.365487,\n                            \"longitude\": 8.519478\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.358843,\n                            \"longitude\": 8.500975\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"L\",\n                    \"lift\": false,\n                    \"floor\": 6,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 12:18:00\",\n                                \"latest\": \"2024-07-12 15:25:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 12:52:00\",\n                                \"latest\": \"2024-07-12 16:38:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"8ba273de-b5d1-4635-831d-abd70ab66ed6\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.408553,\n                            \"longitude\": 8.516963\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.405512,\n                            \"longitude\": 8.547844\n                        }\n                    },\n                    \"item_name\": \"Table\",\n                    \"item_size\": \"M\",\n                    \"lift\": true,\n                    \"floor\": null,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 09:49:00\",\n                                \"latest\": \"2024-07-12 13:05:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 13:11:00\",\n                                \"latest\": \"2024-07-12 15:00:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"d3f3c961-ac24-41d2-82a1-0947f0a0fb3f\",\n            \"items\": [\n                {\n                    \"item_id\": \"6d4e61d5-0c0c-445f-913f-e047a85ff511\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.387253,\n                            \"longitude\": 8.50476\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.356634,\n                            \"longitude\": 8.569094\n                        }\n                    },\n                    \"item_name\": \"Bed\",\n                    \"item_size\": \"L\",\n                    \"lift\": true,\n                    \"floor\": null,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 09:08:00\",\n                                \"latest\": \"2024-07-11 11:34:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 14:28:00\",\n                                \"latest\": \"2024-07-12 16:10:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 10:20:00\",\n                                \"latest\": \"2024-07-11 15:55:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 16:14:00\",\n                                \"latest\": \"2024-07-12 18:18:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"fe08396c-fe6b-4e83-9f8b-95305b6e1135\",\n            \"items\": [\n                {\n                    \"item_id\": \"4c074ec8-5534-4d94-8291-7094e4554616\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.367767,\n                            \"longitude\": 8.553171\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.403441,\n                            \"longitude\": 8.567736\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"M\",\n                    \"lift\": true,\n                    \"floor\": null,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-12 12:42:00\",\n                                \"latest\": \"2024-07-12 16:59:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-12 14:05:00\",\n                                \"latest\": \"2024-07-12 16:27:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        },\n        {\n            \"order_id\": \"31881e26-bdc2-4aaa-8107-fd16747dfaed\",\n            \"items\": [\n                {\n                    \"item_id\": \"553ad160-2a2d-4a79-aade-042b78039c73\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.404003,\n                            \"longitude\": 8.493316\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.40665,\n                            \"longitude\": 8.490392\n                        }\n                    },\n                    \"item_name\": \"Sofa\",\n                    \"item_size\": \"M\",\n                    \"lift\": false,\n                    \"floor\": 6,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 13:14:00\",\n                                \"latest\": \"2024-07-11 16:44:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 12:41:00\",\n                                \"latest\": \"2024-07-12 14:38:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 17:04:00\",\n                                \"latest\": \"2024-07-11 18:28:00\"\n                            },\n                            {\n                                \"earliest\": \"2024-07-12 14:23:00\",\n                                \"latest\": \"2024-07-12 15:43:00\"\n                            }\n                        ]\n                    }\n                },\n                {\n                    \"item_id\": \"90c6761c-1c1b-4994-bf1f-b7e9d1ccf7bf\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.382354,\n                            \"longitude\": 8.510336\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.359414,\n                            \"longitude\": 8.577161\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"M\",\n                    \"lift\": true,\n                    \"floor\": 8,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2024-07-11 10:31:00\",\n                                \"latest\": \"2024-07-11 11:37:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2024-07-11 11:02:00\",\n                                \"latest\": \"2024-07-11 12:48:00\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************:8002/smartx", "protocol": "http", "host": ["13", "53", "125", "227"], "port": "8002", "path": ["smartx"]}}, "response": []}, {"name": "Order Process [Dev]", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": ";2P9(ux]ek*MJTQq", "type": "string"}, {"key": "username", "value": "API_275578_15056936", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n        \"fixation_date\": \"all\",\n        \"price_per_km\": 1,\n        \"price_per_hour\": 140,\n        \"depots\": [\n        {\n            \"depot_id\": \"39e421b0-033e-42f2-b9f6-cb925c2f6ef2\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.371653,\n                \"longitude\": 8.533604\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"bd7751f2-9226-453e-8d00-471216103cd7\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.377687,\n                \"longitude\": 8.519008\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"1e3bd29e-0370-4e4b-843a-5c601a779c23\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.392045,\n                \"longitude\": 8.530583\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        }\n    ],\n        \"orders\": [\n            {\n                \"order_id\": \"0a1ba35b-7be3-40d2-9b4a-56c3184ea9bf\",\n                \"items\": [\n                    {\n                        \"item_id\": \"3ab51b6a-5b62-4201-9991-d29f53bcd6bc\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.390434,\n                                \"longitude\": 8.0457015\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4053885,\n                                \"longitude\": 8.39977\n                            }\n                        },\n                        \"item_name\": \"Sofa\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"item_id\": \"4f7c9805-7d8f-4f8f-b50e-4cc628ca2f15\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.390434,\n                                \"longitude\": 8.0457015\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4053885,\n                                \"longitude\": 8.39977\n                            }\n                        },\n                        \"item_name\": \"Wardrobe\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ]\n                        }\n                    }\n                ]\n            },\n            {\n                \"order_id\": \"57528c50-8ef4-4559-9c56-3efa53585957\",\n                \"items\": [\n                    {\n                        \"item_id\": \"dc3bc0d0-d22f-4b2e-bfc4-e7f44161603a\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.4244818,\n                                \"longitude\": 9.3767173\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4118358,\n                                \"longitude\": 9.339938199999999\n                            }\n                        },\n                        \"item_name\": \"Sofa\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"item_id\": \"d7e5608f-4177-402d-8ed1-c9ff98114ba7\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.4244818,\n                                \"longitude\": 9.3767173\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4118358,\n                                \"longitude\": 9.339938199999999\n                            }\n                        },\n                        \"item_name\": \"Wardrobe\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ]\n                        }\n                    }\n                ]\n            }\n        ],\n        \"items_table\": {\n            \"ItemName\": {\n                \"0\": \"Wardrobe\",\n                \"1\": \"Table\",\n                \"2\": \"Sofa\",\n                \"3\": \"TV\",\n                \"4\": \"Bed\"\n            },\n            \"AvgLoadingTime\": {\n                \"0\": 30,\n                \"1\": 13,\n                \"2\": 20,\n                \"3\": 15,\n                \"4\": 15\n            },\n            \"AvgUnloadingTime\": {\n                \"0\": 40,\n                \"1\": 16,\n                \"2\": 25,\n                \"3\": 20,\n                \"4\": 20\n            },\n            \"SmallFactor\": {\n                \"0\": 0.5,\n                \"1\": 0.5,\n                \"2\": 0.5,\n                \"3\": 0.5,\n                \"4\": 0.5\n            },\n            \"LargeFactor\": {\n                \"0\": 1,\n                \"1\": 1,\n                \"2\": 1,\n                \"3\": 1,\n                \"4\": 1\n            },\n            \"AvgSize\": {\n                \"0\": 300,\n                \"1\": 150,\n                \"2\": 200,\n                \"3\": 100,\n                \"4\": 250\n            },\n            \"AvgWeight\": {\n                \"0\": 80,\n                \"1\": 40,\n                \"2\": 50,\n                \"3\": 20,\n                \"4\": 70\n            }\n        }\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************:8002/smartx", "protocol": "http", "host": ["13", "53", "125", "227"], "port": "8002", "path": ["smartx"]}}, "response": []}, {"name": "Order Process [Dev] Copy", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": ";2P9(ux]ek*MJTQq", "type": "string"}, {"key": "username", "value": "API_275578_15056936", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n        \"fixation_date\": \"all\",\n        \"price_per_km\": 1,\n        \"price_per_hour\": 140,\n        \"depots\": [\n        {\n            \"depot_id\": \"39e421b0-033e-42f2-b9f6-cb925c2f6ef2\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.371653,\n                \"longitude\": 8.533604\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"bd7751f2-9226-453e-8d00-471216103cd7\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.377687,\n                \"longitude\": 8.519008\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        },\n        {\n            \"depot_id\": \"1e3bd29e-0370-4e4b-843a-5c601a779c23\",\n            \"num_vehicles\": 1,\n            \"position\": {\n                \"latitude\": 47.392045,\n                \"longitude\": 8.530583\n            },\n            \"capacity\": {\n                \"size\": 30,\n                \"weight\": 5000\n            }\n        }\n    ],\n        \"orders\": [\n            {\n                \"order_id\": \"0a1ba35b-7be3-40d2-9b4a-56c3184ea9bf\",\n                \"items\": [\n                    {\n                        \"item_id\": \"3ab51b6a-5b62-4201-9991-d29f53bcd6bc\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.390434,\n                                \"longitude\": 8.0457015\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4053885,\n                                \"longitude\": 8.39977\n                            }\n                        },\n                        \"item_name\": \"Sofa\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"item_id\": \"4f7c9805-7d8f-4f8f-b50e-4cc628ca2f15\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.390434,\n                                \"longitude\": 8.0457015\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4053885,\n                                \"longitude\": 8.39977\n                            }\n                        },\n                        \"item_name\": \"Wardrobe\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 09:30:00\",\n                                    \"latest\": \"2024-10-09 11:00:00\"\n                                }\n                            ]\n                        }\n                    }\n                ]\n            },\n            {\n                \"order_id\": \"57528c50-8ef4-4559-9c56-3efa53585957\",\n                \"items\": [\n                    {\n                        \"item_id\": \"dc3bc0d0-d22f-4b2e-bfc4-e7f44161603a\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.4244818,\n                                \"longitude\": 9.3767173\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4118358,\n                                \"longitude\": 9.339938199999999\n                            }\n                        },\n                        \"item_name\": \"Sofa\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ]\n                        }\n                    },\n                    {\n                        \"item_id\": \"d7e5608f-4177-402d-8ed1-c9ff98114ba7\",\n                        \"position\": {\n                            \"pickup\": {\n                                \"latitude\": 47.4244818,\n                                \"longitude\": 9.3767173\n                            },\n                            \"delivery\": {\n                                \"latitude\": 47.4118358,\n                                \"longitude\": 9.339938199999999\n                            }\n                        },\n                        \"item_name\": \"Wardrobe\",\n                        \"item_size\": \"L\",\n                        \"lift\": false,\n                        \"floor\": 2,\n                        \"time_windows\": {\n                            \"pickup\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ],\n                            \"delivery\": [\n                                {\n                                    \"earliest\": \"2024-10-09 07:30:00\",\n                                    \"latest\": \"2024-10-09 16:00:00\"\n                                }\n                            ]\n                        }\n                    }\n                ]\n            }\n        ],\n        \"items_table\": {\n            \"ItemName\": {\n                \"0\": \"Wardrobe\",\n                \"1\": \"Table\",\n                \"2\": \"Sofa\",\n                \"3\": \"TV\",\n                \"4\": \"Bed\"\n            },\n            \"AvgLoadingTime\": {\n                \"0\": 30,\n                \"1\": 13,\n                \"2\": 20,\n                \"3\": 15,\n                \"4\": 15\n            },\n            \"AvgUnloadingTime\": {\n                \"0\": 40,\n                \"1\": 16,\n                \"2\": 25,\n                \"3\": 20,\n                \"4\": 20\n            },\n            \"SmallFactor\": {\n                \"0\": 0.5,\n                \"1\": 0.5,\n                \"2\": 0.5,\n                \"3\": 0.5,\n                \"4\": 0.5\n            },\n            \"LargeFactor\": {\n                \"0\": 1,\n                \"1\": 1,\n                \"2\": 1,\n                \"3\": 1,\n                \"4\": 1\n            },\n            \"AvgSize\": {\n                \"0\": 300,\n                \"1\": 150,\n                \"2\": 200,\n                \"3\": 100,\n                \"4\": 250\n            },\n            \"AvgWeight\": {\n                \"0\": 80,\n                \"1\": 40,\n                \"2\": 50,\n                \"3\": 20,\n                \"4\": 70\n            }\n        }\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************:8002/smartx", "protocol": "http", "host": ["13", "53", "125", "227"], "port": "8002", "path": ["smartx"]}}, "response": []}, {"name": "itx order estimation", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": ";2P9(ux]ek*MJTQq", "type": "string"}, {"key": "username", "value": "API_275578_15056936", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n        \"price_per_km\": 1,\n        \"price_per_hour\": 140,\n        \"depots\": [\n            {\n                \"depot_id\": \"ea9278c9-6417-4dc7-bdae-707a9ed14725\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.188363,\n                    \"longitude\": 6.104184\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"086eec8b-6c8d-4e96-932a-34f947118d45\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.3636973,\n                    \"longitude\": 8.5683477\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"0c46d4b6-0f7a-4f57-bcda-a69750553825\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.3924952,\n                    \"longitude\": 8.4750879\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"80872e80-1ea7-4655-9b7e-390d0fdac76f\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.35686731,\n                    \"longitude\": 8.5241576\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"5a542a0e-f6c2-4418-be96-f8a2db235dcc\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.4108113,\n                    \"longitude\": 8.5564106\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"6fe430d6-bda3-4e33-b2eb-854f801be248\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.194643,\n                    \"longitude\": 6.1828394\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"374198d9-a9d5-493f-b477-2130b856c83c\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.2410328,\n                    \"longitude\": 6.1467088\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"acd9b667-3d83-47c3-8d87-60dbefdf9466\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.5297723,\n                    \"longitude\": 7.5974578\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"b67a71c9-453d-49ef-8356-40d71d514aea\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.5321539,\n                    \"longitude\": 6.5532195\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"607842dd-752b-4177-9c84-24aea2554a2e\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.5773637,\n                    \"longitude\": 7.5918027\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"1ba0f05a-6dec-43b5-b0bb-0d3ed7d16cc5\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.5222386,\n                    \"longitude\": 6.6303243\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"ce055819-f5be-4115-b03e-be74588b912f\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.9350158,\n                    \"longitude\": 7.4184004\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"dc0f77fe-ab9a-41d4-8045-d2bf017f9c53\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.504407,\n                    \"longitude\": 8.7236753\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"a78d3328-8a4f-4516-ae11-9f92633fca8a\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.9560393,\n                    \"longitude\": 7.4499792\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"d89f9a92-ed8e-4071-9308-4ee4ddc98191\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.5058863,\n                    \"longitude\": 8.7003514\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"eb167242-1627-4e92-b9c9-d72a8d8cbd51\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.0310013,\n                    \"longitude\": 8.2722331\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"fc7b137d-67fc-4e86-a926-a620fc980950\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.055807,\n                    \"longitude\": 8.3098812\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"0b71aa50-4ba1-4bae-bdde-576e651840ea\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.418056,\n                    \"longitude\": 9.3593279\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"fc10a120-b469-4d54-b6d8-ac7f4a577209\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.0032764,\n                    \"longitude\": 8.9247703\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"be58d9c2-6d7f-4910-921c-2922a2c50390\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.4354622,\n                    \"longitude\": 9.3743676\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"e86260a6-4ce2-4ff1-a321-4709b171f2e5\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 46.0161298,\n                    \"longitude\": 8.9440994\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"3bdba02d-f26a-4948-968b-a8e4ae40fd74\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.1284679,\n                    \"longitude\": 7.2470419\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            },\n            {\n                \"depot_id\": \"c403f66a-14fb-4157-9f8f-5c3db63a3e56\",\n                \"num_vehicles\": 50,\n                \"position\": {\n                    \"latitude\": 47.1311436,\n                    \"longitude\": 7.2571747\n                },\n                \"capacity\": {\n                    \"size\": 30,\n                    \"weight\": 1050\n                }\n            }\n        ],\n        \"order\": {\n            \"order_id\": \"17029752-6343-4d0c-8b8a-ed5274ea2613\",\n            \"items\": [\n                {\n                    \"item_id\": \"0de8229c-8a36-41f1-919f-940fdce36564\",\n                    \"position\": {\n                        \"pickup\": {\n                            \"latitude\": 47.36013759999999,\n                            \"longitude\": 8.5293292\n                        },\n                        \"delivery\": {\n                            \"latitude\": 47.3965618,\n                            \"longitude\": 8.5042751\n                        }\n                    },\n                    \"item_name\": \"TV\",\n                    \"item_size\": \"M\",\n                    \"lift\": false,\n                    \"floor\": 2,\n                    \"time_windows\": {\n                        \"pickup\": [\n                            {\n                                \"earliest\": \"2021-10-12 10:00:00\",\n                                \"latest\": \"2021-10-12 14:30:00\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-13 10:00:00\",\n                                \"latest\": \"2021-10-13 14:30:00\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-14 10:00:00\",\n                                \"latest\": \"2021-10-14 14:30:00\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-15 10:00:00\",\n                                \"latest\": \"2021-10-15 14:30:00\"\n                            }\n                        ],\n                        \"delivery\": [\n                            {\n                                \"earliest\": \"2021-10-12 10:00:00\",\n                                \"latest\": \"2021-10-12 21:59:59\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-13 10:00:00\",\n                                \"latest\": \"2021-10-13 21:59:59\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-14 10:00:00\",\n                                \"latest\": \"2021-10-14 21:59:59\"\n                            },\n                            {\n                                \"earliest\": \"2021-10-15 10:00:00\",\n                                \"latest\": \"2021-10-15 21:59:59\"\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************:8002/ixt", "protocol": "http", "host": ["13", "53", "125", "227"], "port": "8002", "path": ["ixt"]}}, "response": []}], "description": "Backend API"}, {"name": "<PERSON><PERSON>", "item": [{"name": "Category", "item": [{"name": "ItemCategoryList", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/item-categories", "host": ["{{api-localhost}}"], "path": ["item-categories"]}}, "response": []}, {"name": "ItemCategory Create", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Table new item\",\n    \"description\": \"This is a table\",\n    \"icon\": \"table_wood.svg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/item-category", "host": ["{{api-localhost}}"], "path": ["item-category"]}}, "response": []}, {"name": "ItemCategory Update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n            \"name\": \"Fridge update\",\n            \"description\": \"Various types of fridges\",\n            \"icon\": \"fridge-icon.png\"\n           \n        }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/item-category/1", "host": ["{{api-localhost}}"], "path": ["item-category", "1"]}}, "response": []}, {"name": "Item<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/item-category/9", "host": ["{{api-localhost}}"], "path": ["item-category", "9"]}}, "response": []}]}, {"name": "Item Create", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Wardrobe 123\",\n    \"description\": \"Wardrobe description 123\",\n    \"picture\": \"closet.svg\",\n    \"size\": \"large\",\n    \"weight\": \"heavy\",\n    \"avgLoadingTime\": 40,\n    \"avgUnloadingTime\": 60,\n    \"avgWeight\": 150,\n    \"avgSize\": 10,\n    \"smallFactor\": \"0.8\",\n    \"largeFactor\": \"1.0\",\n    \"itemCategoryId\": \"4\",\n    \"isSizeConfigurable\": true,\n    \"isWeightConfigurable\": true,\n    \"isAssembleConfigurable\": true,\n    \"isQuantityConfigurable\": false,\n    \"isDescriptionConfigurable\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/item", "host": ["{{api-localhost}}"], "path": ["item"]}}, "response": []}, {"name": "Item Update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n               \n                \"name\": \"Double Door Fridge update\",\n                \"description\": \"Spacious double door refrigerator\",\n                \"picture\": \"double_door_fridge.png\",\n                \"size\": \"large\",\n                \"weight\": \"heavy\",\n                \"avgLoadingTime\": 50,\n                \"avgUnloadingTime\": 50,\n                \"avgWeight\": 200,\n                \"avgSize\": 12,\n                \"smallFactor\": \"0.9\",\n                \"largeFactor\": \"1.8\",\n                \"itemCategoryId\": 1,\n                \"isSizeConfigurable\": false,\n                \"isWeightConfigurable\": false,\n                \"isAssembleConfigurable\": false,\n                \"isQuantityConfigurable\": true,\n                \"isDescriptionConfigurable\": true\n                \n            }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/item/1", "host": ["{{api-localhost}}"], "path": ["item", "1"]}}, "response": []}, {"name": "Item List", "request": {"method": "GET", "header": [], "url": {"raw": "{{api-localhost}}/items?page=1&rowsPerPage=15", "host": ["{{api-localhost}}"], "path": ["items"], "query": [{"key": "page", "value": "1"}, {"key": "rowsPerPage", "value": "15"}]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/carriers?page=1&rowsPerPage=5&sortBy=createdAt&descending=true", "host": ["{{api-localhost}}"], "path": ["carriers"], "query": [{"key": "page", "value": "1"}, {"key": "rowsPerPage", "value": "5"}, {"key": "sortBy", "value": "createdAt"}, {"key": "descending", "value": "true"}]}}, "response": []}]}, {"name": "Route", "item": [{"name": "Route list", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/routes?page=1&rowsPerPage=10", "host": ["{{api-localhost}}"], "path": ["routes"], "query": [{"key": "page", "value": "1"}, {"key": "rowsPerPage", "value": "10"}]}}, "response": []}, {"name": "Route Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/route/1", "host": ["{{api-localhost}}"], "path": ["route", "1"]}}, "response": []}, {"name": "Closests carriers", "request": {"method": "GET", "header": [{"key": "Cache-Control", "value": "no-cache", "type": "text"}, {"key": "Postman-To<PERSON>", "value": "<calculated when request is sent>", "type": "text"}, {"key": "Host", "value": "<calculated when request is sent>", "type": "text"}, {"key": "User-Agent", "value": "PostmanRuntime/7.39.1", "type": "text"}, {"key": "Accept", "value": "*/*", "type": "text"}, {"key": "Accept-Encoding", "value": "gzip, deflate, br", "type": "text"}, {"key": "Connection", "value": "keep-alive", "type": "text"}, {"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/closest-movers-for-route/1", "host": ["{{api-localhost}}"], "path": ["closest-movers-for-route", "1"]}}, "response": []}], "description": "### All routes related urls are here"}, {"name": "Entity status history", "item": [{"name": "Entity status history list", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/entity-status-history?page=1&rowsPerPage=10", "host": ["{{api-localhost}}"], "path": ["entity-status-history"], "query": [{"key": "page", "value": "1"}, {"key": "rowsPerPage", "value": "10"}]}}, "response": []}, {"name": "Entity status history details by entity id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/entity-status-history/route/1", "host": ["{{api-localhost}}"], "path": ["entity-status-history", "route", "1"]}}, "response": []}], "description": "### All entity status history related urls are here"}, {"name": "Order", "item": [{"name": "Order details", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"company\": \"111\",\n    \"phone\": \"*********\",\n    \"confirmationDate\": \"03-01-2025\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/order/8", "host": ["{{api-localhost}}"], "path": ["order", "8"]}}, "response": []}, {"name": "Order update", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 8,\n    \"status\": \"submitted\",\n    \"amount\": null,\n    \"reservationFee\": null,\n    \"estimatedPrice\": null,\n    \"confirmationDate\": null,\n    \"pickUpLocation\": {\n        \"firstName\": \"<PERSON><PERSON><PERSON>\",\n        \"lastName\": \"<PERSON>\",\n        \"address\": \"District 11, Zürich, Switzerland\",\n        \"floor\": 2,\n        \"hasElevator\": true,\n        \"lat\": 47.4138487,\n        \"lng\": 8.5454908\n    },\n    \"dropOffLocation\": {\n        \"firstName\": \"<PERSON><PERSON>\",\n        \"lastName\": \"Worker\",\n        \"address\": \"Zurich town hall, 8001 Zürich, Switzerland\",\n        \"floor\": 3,\n        \"hasElevator\": true,\n        \"lat\": 47.3719584,\n        \"lng\": 8.5442926\n    },\n    \"billingInfo\": {\n        \"email\": \"<EMAIL>\",\n        \"firstName\": \"Md <PERSON>auddin\",\n        \"lastName\": \"Rana\",\n        \"company\": \"111\",\n        \"phone\": \"*********\"\n    },\n    \"customer\": {\n        \"id\": 1,\n        \"firstName\": \"<PERSON>d <PERSON>din\",\n        \"lastName\": \"Rana\",\n        \"street\": null,\n        \"postalCode\": null,\n        \"city\": null,\n        \"email\": \"<EMAIL>\",\n        \"mobile\": \"*********\",\n        \"loginCode\": null,\n        \"preferredPaymentMethod\": null,\n        \"birthdate\": null,\n        \"companyName\": \"111\",\n        \"contactType\": null,\n        \"isActive\": true,\n        \"isDeleted\": false,\n        \"createdAt\": \"2025-01-24T21:22:56.816Z\",\n        \"updatedAt\": \"2025-01-24T21:22:56.816Z\"\n    },\n    \"orderItems\": [\n        {\n            \"id\": null,\n            \"item\": {\n                \"id\": 1,\n                \"name\": \"Double Door Fridge\",\n                \"size\": \"large\",\n                \"description\": \"Spacious double door refrigerator\"\n            },\n            \"dropOffLocation\": {\n                \"firstName\": \"Michal\",\n                \"lastName\": \"Worker\",\n                \"address\": \"Zurich town hall, 8001 Zürich, Switzerland\",\n                \"floor\": 3,\n                \"hasElevator\": true,\n                \"lat\": 47.3719584,\n                \"lng\": 8.5442926\n            },\n            \"itemConfiguration\": {\n                \"selfAssemble\": true,\n                \"size\": \"Small\",\n                \"avgLoadingTime\": 50,\n                \"avgUnloadingTime\": 50,\n                \"avgSize\": 12,\n                \"avgWeight\": 200,\n                \"smallFactor\": \"0.9\",\n                \"largeFactor\": \"1.8\"\n            },\n            \"description\": \"Fridge\"\n        },\n        {\n            \"id\": 4,\n            \"item\": {\n                \"id\": 4,\n                \"name\": \"Front Load Washer\",\n                \"size\": \"large\",\n                \"description\": \"Efficient front load washing machine\"\n            },\n            \"dropOffLocation\": {\n                \"firstName\": \"Michal\",\n                \"lastName\": \"Worker\",\n                \"address\": \"Zurich town hall, 8001 Zürich, Switzerland\",\n                \"floor\": 3,\n                \"hasElevator\": true,\n                \"lat\": 47.3719584,\n                \"lng\": 8.5442926\n            },\n            \"itemConfiguration\": {\n                \"selfAssemble\": true,\n                \"size\": \"Small\",\n                \"avgLoadingTime\": 60,\n                \"avgUnloadingTime\": 60,\n                \"avgSize\": 15,\n                \"avgWeight\": 250,\n                \"smallFactor\": \"1\",\n                \"largeFactor\": \"2\"\n            },\n            \"description\": \"Washing Machines\"\n        }\n    ],\n    \"timeWindows\": [\n        {\n            \"earliestPickUp\": \"2025-02-01T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-01T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-01T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-01T17:00:00.000Z\"\n        },\n        {\n            \"earliestPickUp\": \"2025-02-02T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-02T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-02T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-02T17:00:00.000Z\"\n        },\n        {\n            \"earliestPickUp\": \"2025-02-03T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-03T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-03T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-03T17:00:00.000Z\"\n        },\n        {\n            \"earliestPickUp\": \"2025-02-04T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-04T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-04T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-04T17:00:00.000Z\"\n        },\n        {\n            \"earliestPickUp\": \"2025-02-05T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-05T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-05T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-05T17:00:00.000Z\"\n        },\n        {\n            \"earliestPickUp\": \"2025-02-06T08:00:00.000Z\",\n            \"latestPickUp\": \"2025-02-06T09:00:00.000Z\",\n            \"earliestDelivery\": \"2025-02-06T08:00:00.000Z\",\n            \"latestDelivery\": \"2025-02-06T17:00:00.000Z\"\n        }\n    ],\n    \"createdAt\": \"2025-01-25T11:49:42.188Z\",\n    \"updatedAt\": \"2025-01-25T11:50:00.082Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/order/8", "host": ["{{api-localhost}}"], "path": ["order", "8"]}}, "response": []}, {"name": "Order Create", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"orderItems\": [\n    {\n            \"id\": 2,\n            \"name\": \"Washing Machines\",\n            \"description\": \"Different types of washing machines\",\n            \"icon\": \"washing-machine-icon.png\",\n            \"types\": [\n                {\n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n                }\n             \n            ],\n     \"remarks\": \"Handle with care\",\n      \"isDifferentDropOff\": true,\n      \"dropOffLocation\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"Doe\",\n        \"address\": \"123 Elm Street\",\n        \"floor\": 3,\n        \"hasElevator\": true,\n        \"lat\": 40.712776,\n        \"lng\": -74.005974\n      },\n      \"type\": {\n      \n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n      },\n      \"size\": \"small\",\n      \"weight\": \"heavy\",\n      \"selfAssemble\": false\n    }\n\n  ],\n  \"destination\": {\n    \"pickUpLocation\": {\n      \"firstName\": \"Eum cum facere ipsum\",\n      \"lastName\": \"Necessitatibus cum a\",\n      \"address\": \"Dolorum debitis odio\",\n      \"floor\": 73,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"dropOffLocation\": {\n      \"firstName\": \"Architecto soluta ut\",\n      \"lastName\": \"Totam ipsum soluta m\",\n      \"address\": \"Et ab voluptatem Mi\",\n      \"floor\": 24,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"isDifferentDropOff\": false,\n    \"differentDropOffItems\": [\"4e7e12f8-13ab-42a8-b61b-761b1e4501d2\"]\n  },\n  \"timeWindows\": [\n    {\n      \"date\": [\n        \"2025-01-05T11:17:23.180Z\",\n        \"2025-01-07T11:17:23.180Z\"\n      ],\n      \"start_time\": \"08:00\",\n      \"end_time\": \"09:00\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/order", "host": ["{{api-localhost}}"], "path": ["order"]}}, "response": []}, {"name": "Payment Init", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/payment-init/7", "host": ["{{api-localhost}}"], "path": ["payment-init", "7"]}}, "response": []}, {"name": "Payment Init DEV", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/payment-init/06d9bb61-43ee-41f9-a679-73409b02730f", "host": ["{{api-localhost}}"], "path": ["payment-init", "06d9bb61-43ee-41f9-a679-73409b02730f"]}}, "response": []}, {"name": "Order Details by token (status submitted))", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/order/644285dc-03f3-4fa6-8588-b1bbe34d3001", "host": ["{{api-localhost}}"], "path": ["order", "644285dc-03f3-4fa6-8588-b1bbe34d3001"]}}, "response": []}, {"name": "Order fixiation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/orders-for-fixiation", "host": ["{{api-localhost}}"], "path": ["orders-for-fixiation"]}}, "response": []}, {"name": "Order Price Estimation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/order-price-estimation/1", "host": ["{{api-localhost}}"], "path": ["order-price-estimation", "1"]}}, "response": []}, {"name": "Order-list", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/orders", "host": ["{{api-localhost}}"], "path": ["orders"]}}, "response": []}, {"name": "customer order", "request": {"method": "PUT", "header": [], "url": {"raw": "{{api-localhost}}/customer-order/1", "host": ["{{api-localhost}}"], "path": ["customer-order", "1"]}}, "response": []}, {"name": "order-update", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"orderItems\": [\n    {\n            \"id\": 2,\n            \"name\": \"Washing Machines\",\n            \"description\": \"Different types of washing machines\",\n            \"icon\": \"washing-machine-icon.png\",\n            \"types\": [\n                {\n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n                }\n             \n            ],\n     \"remarks\": \"Handle with care\",\n      \"isDifferentDropOff\": true,\n      \"dropOffLocation\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"Doe\",\n        \"address\": \"123 Elm Street\",\n        \"floor\": 3,\n        \"hasElevator\": true,\n        \"lat\": 40.712776,\n        \"lng\": -74.005974\n      },\n      \"type\": {\n      \n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n      },\n      \"size\": \"small\",\n      \"weight\": \"heavy\",\n      \"selfAssemble\": false\n    }\n\n  ],\n  \"destination\": {\n    \"pickUpLocation\": {\n      \"firstName\": \"Eum cum facere ipsum\",\n      \"lastName\": \"Necessitatibus cum a\",\n      \"address\": \"Dolorum debitis odio\",\n      \"floor\": 73,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"dropOffLocation\": {\n      \"firstName\": \"Architecto soluta ut\",\n      \"lastName\": \"Totam ipsum soluta m\",\n      \"address\": \"Et ab voluptatem Mi\",\n      \"floor\": 24,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"isDifferentDropOff\": false,\n    \"differentDropOffItems\": [\"4e7e12f8-13ab-42a8-b61b-761b1e4501d2\"]\n  },\n  \"timeWindows\": [\n    {\n      \"date\": [\n        \"2025-01-05T11:17:23.180Z\",\n        \"2025-01-05T11:17:23.180Z\"\n      ],\n      \"start_time\": \"08:00\",\n      \"end_time\": \"09:00\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/order/12", "host": ["{{api-localhost}}"], "path": ["order", "12"]}}, "response": []}]}, {"name": "Transections", "item": [{"name": "Capture transaction-requestId", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/check-transaction/e290c996-7678-4f35-9da4-98c8a0466fe6?token=ehjmyl7eq80yxc6lbjinbuqft", "host": ["{{api-localhost}}"], "path": ["check-transaction", "e290c996-7678-4f35-9da4-98c8a0466fe6"], "query": [{"key": "token", "value": "ehjmyl7eq80yxc6lbjinbuqft"}]}}, "response": []}, {"name": "capture-transaction (requestId)", "request": {"method": "GET", "header": [], "url": {"raw": "{{api-localhost}}/capture-transaction/ad9d2c7d-ea91-4163-8346-870e232e4872?transactionId=6Mhl5MAvYKEEtAx1Qt2SA9f5Uf5A", "host": ["{{api-localhost}}"], "path": ["capture-transaction", "ad9d2c7d-ea91-4163-8346-870e232e4872"], "query": [{"key": "transactionId", "value": "6Mhl5MAvYKEEtAx1Qt2SA9f5Uf5A"}]}}, "response": []}]}, {"name": "Customer", "item": [{"name": "Customer List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/customers", "host": ["{{api-localhost}}"], "path": ["customers"]}}, "response": []}, {"name": "Customer by id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/customer/1", "host": ["{{api-localhost}}"], "path": ["customer", "1"]}}, "response": []}, {"name": "Customer update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n        \"firstName\": \"<PERSON><PERSON> update\",\n        \"lastName\": \"<PERSON>\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/customer/1", "host": ["{{api-localhost}}"], "path": ["customer", "1"]}}, "response": []}, {"name": "Customer delete", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/customer/1", "host": ["{{api-localhost}}"], "path": ["customer", "1"]}}, "response": []}]}, {"name": "Movers", "item": [{"name": "Movers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/movers?page=1&rowsPerPage=3", "host": ["{{api-localhost}}"], "path": ["movers"], "query": [{"key": "page", "value": "1"}, {"key": "rowsPerPage", "value": "3"}]}}, "response": []}, {"name": "get Mover by id", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/mover/1", "host": ["{{api-localhost}}"], "path": ["mover", "1"]}}, "response": []}, {"name": "mover update", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "   {\n        \"companyName\": \"ViTransport update\",\n        \"independent\": true,\n        \"firstName\": \"<PERSON><PERSON><PERSON>\",\n        \"lastName\": \"Kazakov\"\n      \n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/mover/1", "host": ["{{api-localhost}}"], "path": ["mover", "1"]}}, "response": []}, {"name": "mover delete", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "   {\n        \"companyName\": \"ViTransport update\",\n        \"independent\": true,\n        \"firstName\": \"<PERSON><PERSON><PERSON>\",\n        \"lastName\": \"Kazakov\"\n      \n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/mover/3", "host": ["{{api-localhost}}"], "path": ["mover", "3"]}}, "response": []}, {"name": "mover-create", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"companyName\": \"QuickMove Logistics\",\n  \"independent\": true,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\",\n  \"startingLat\": 40.712776,\n  \"startingLng\": -74.005974,\n  \"street\": \"123 Main Street\",\n  \"postalCode\": \"10001\",\n  \"city\": \"New York\",\n  \"email\": \"<EMAIL>\",\n  \"mobile\": \"1234567890\",\n  \"size\": 20.5,\n  \"weight\": 1500.75,\n  \"numOfVehicles\": 5,\n  \"lang\": \"en\"\n}\n\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/mover", "host": ["{{api-localhost}}"], "path": ["mover"]}}, "response": []}]}, {"name": "v2 (client-only)", "item": [{"name": "items", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/items", "host": ["{{api-localhost}}"], "path": ["v2", "items"]}}, "response": []}, {"name": "Order Create", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orderItems\": [\n        {\n            \"id\": 2,\n            \"name\": \"Washing Machines\",\n            \"description\": \"Different types of washing machines\",\n            \"icon\": \"washing-machine-icon.png\",\n            \"types\": [\n                {\n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n                }\n            ],\n            \"remarks\": \"Handle with care\",\n            \"isDifferentDropOff\": true,\n            \"dropOffLocation\": {\n                \"firstName\": \"<PERSON>\",\n                \"lastName\": \"Doe\",\n                \"address\": \"123 Elm Street\",\n                \"floor\": 3,\n                \"hasElevator\": true,\n                \"lat\": 40.712776,\n                \"lng\": -74.005974\n            },\n            \"type\": {\n                \"id\": 6,\n                \"name\": \"Compact Washer\",\n                \"description\": \"Compact washing machine for small spaces\"\n            },\n            \"size\": \"small\",\n            \"weight\": \"heavy\",\n            \"selfAssemble\": false\n        }\n    ],\n    \"destination\": {\n        \"pickUpLocation\": {\n            \"firstName\": \"Eum cum facere ipsum\",\n            \"lastName\": \"Necessitatibus cum a\",\n            \"address\": \"Dolorum debitis odio\",\n            \"floor\": 73,\n            \"hasElevator\": true,\n            \"lat\": 0,\n            \"lng\": 0\n        },\n        \"dropOffLocation\": {\n            \"firstName\": \"Architecto soluta ut\",\n            \"lastName\": \"Totam ipsum soluta m\",\n            \"address\": \"Et ab voluptatem Mi\",\n            \"floor\": 24,\n            \"hasElevator\": true,\n            \"lat\": 0,\n            \"lng\": 0\n        },\n        \"isDifferentDropOff\": false,\n        \"differentDropOffItems\": [\n            \"4e7e12f8-13ab-42a8-b61b-761b1e4501d2\"\n        ]\n    },\n    \"timeWindows\": [\n        {\n            \"date\": [\n                \"2025-01-05T11:17:23.180Z\",\n                \"2025-01-07T11:17:23.180Z\"\n            ],\n            \"start_time\": \"08:00\",\n            \"end_time\": \"09:00\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order", "host": ["{{api-localhost}}"], "path": ["v2", "order"]}}, "response": []}, {"name": "Order Confirm", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"company\": \"111\",\n    \"phone\": \"*********\",\n    \"confirmationDate\": \"2025-01-05T11:17:23.180Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/11/confirm", "host": ["{{api-localhost}}"], "path": ["v2", "order", "11", "confirm"]}}, "response": []}, {"name": "Order Confirm Copy", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"company\": \"111\",\n    \"phone\": \"*********\",\n    \"confirmationDate\": \"2025-01-05T11:17:23.180Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/15/cancel/client?tokenId=96ceda52-5fe9-4bd7-a5c7-a0b055d4672a", "host": ["{{api-localhost}}"], "path": ["v2", "order", "15", "cancel", "client"], "query": [{"key": "tokenId", "value": "96ceda52-5fe9-4bd7-a5c7-a0b055d4672a"}]}}, "response": []}, {"name": "order update", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"orderItems\": [\n    {\n            \"id\": 2,\n            \"name\": \"Washing Machines\",\n            \"description\": \"Different types of washing machines\",\n            \"icon\": \"washing-machine-icon.png\",\n            \"types\": [\n                {\n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n                }\n             \n            ],\n     \"remarks\": \"Handle with care\",\n      \"isDifferentDropOff\": true,\n      \"dropOffLocation\": {\n        \"firstName\": \"<PERSON>\",\n        \"lastName\": \"Doe\",\n        \"address\": \"123 Elm Street\",\n        \"floor\": 3,\n        \"hasElevator\": true,\n        \"lat\": 40.712776,\n        \"lng\": -74.005974\n      },\n      \"type\": {\n      \n                    \"id\": 6,\n                    \"name\": \"Compact Washer\",\n                    \"description\": \"Compact washing machine for small spaces\"\n      },\n      \"size\": \"small\",\n      \"weight\": \"heavy\",\n      \"selfAssemble\": false\n    }\n\n  ],\n  \"destination\": {\n    \"pickUpLocation\": {\n      \"firstName\": \"Eum cum facere ipsum\",\n      \"lastName\": \"Necessitatibus cum a\",\n      \"address\": \"Dolorum debitis odio\",\n      \"floor\": 73,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"dropOffLocation\": {\n      \"firstName\": \"Architecto soluta ut\",\n      \"lastName\": \"Totam ipsum soluta m\",\n      \"address\": \"Et ab voluptatem Mi\",\n      \"floor\": 24,\n      \"hasElevator\": true,\n      \"lat\": 0,\n      \"lng\": 0\n    },\n    \"isDifferentDropOff\": false,\n    \"differentDropOffItems\": [\"4e7e12f8-13ab-42a8-b61b-761b1e4501d2\"]\n  },\n  \"timeWindows\": [\n    {\n      \"date\": [\n        \"2025-01-05T11:17:23.180Z\",\n        \"2025-01-05T11:17:23.180Z\"\n      ],\n      \"start_time\": \"08:00\",\n      \"end_time\": \"09:00\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/1", "host": ["{{api-localhost}}"], "path": ["v2", "order", "1"]}}, "response": []}, {"name": "Order details", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"company\": \"111\",\n    \"phone\": \"*********\",\n    \"confirmationDate\": \"03-01-2025\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/7", "host": ["{{api-localhost}}"], "path": ["v2", "order", "7"]}}, "response": []}, {"name": "Order client details", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/15/client-details", "host": ["{{api-localhost}}"], "path": ["v2", "order", "15", "client-details"]}}, "response": []}, {"name": "order delete", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{api-localhost}}/v2/order/c2887a6d-eda2-4133-8c75-ede60e21f517", "host": ["{{api-localhost}}"], "path": ["v2", "order", "c2887a6d-eda2-4133-8c75-ede60e21f517"]}}, "response": []}, {"name": "Order Cancel", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/2/cancel/client", "host": ["{{api-localhost}}"], "path": ["v2", "order", "2", "cancel", "client"]}}, "response": []}, {"name": "Order Cost estimation", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/7/cost", "host": ["{{api-localhost}}"], "path": ["v2", "order", "7", "cost"]}}, "response": []}, {"name": "Estimate Cost", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"items\": [1,2,3],\n  \"dropOffLocation\": {\n    \"lat\": 47.4053885,\n    \"lng\": 8.39977},\n  \"pickUpLocation\": {\n    \"lat\": 47.4244818,\n    \"lng\": 9.3767173},\n   \"timeWindows\": [\n    {\n            \"date\": [\n                \"2025-01-05T11:17:23.180Z\",\n                \"2025-01-05T11:17:23.180Z\"\n            ],\n            \"start_time\": \"08:00\",\n            \"end_time\": \"17:00\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/v2/order/estimate-cost", "host": ["{{api-localhost}}"], "path": ["v2", "order", "estimate-cost"]}}, "response": []}, {"name": "paymnet-init", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/v2/order/11/payment", "host": ["{{api-localhost}}"], "path": ["v2", "order", "11", "payment"]}}, "response": []}, {"name": "Order items details by order id", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "url": {"raw": "{{api-localhost}}/v2/order/1/order-items", "host": ["{{api-localhost}}"], "path": ["v2", "order", "1", "order-items"]}}, "response": []}], "description": "New client apis"}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"123456\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/login", "host": ["{{api-localhost}}"], "path": ["login"]}}, "response": []}, {"name": "register", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"shahriar\",\n  \"lastName\": \"shakil\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\",\n  \"confirmPassword\": \"123456\",\n  \"phoneNumber\": \"1234567890\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-localhost}}/register", "host": ["{{api-localhost}}"], "path": ["register"]}}, "response": []}, {"name": "mover-action", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"shahriar\",\n  \"lastName\": \"shakil\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\",\n  \"confirmPassword\": \"123456\",\n  \"phoneNumber\": \"1234567890\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-WebRoutes}}/route/1/mover/2/action", "host": ["{{api-WebRoutes}}"], "path": ["route", "1", "mover", "2", "action"]}}, "response": []}, {"name": "order fixation", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json()", "pm.collectionVariables.set(\"tixpi-backend-auth-token\", response.data.accessToken);"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{tixpi-backend-auth-token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"shahriar\",\n  \"lastName\": \"shakil\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\",\n  \"confirmPassword\": \"123456\",\n  \"phoneNumber\": \"1234567890\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{api-WebRoutes}}/order-fixation", "host": ["{{api-WebRoutes}}"], "path": ["order-fixation"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "tixpi-backend-auth-token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWRlbnRpZmllciI6MSwicmVmcmVzaFRva2VuIjoiZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnBaQ0k2TVN3aWFXRjBJam94TnpNM05UVXpNVEV3TENKbGVIQWlPakUzTkRBeE5EVXhNVEI5LjJudmF5OEg5WVVIWjI1WXJXMUVET2NfbDlBU0lId3NDTjMtM2d0UEw5RzAiLCJpYXQiOjE3Mzc1NTMxMTAsImV4cCI6MTczNzU1NjcxMH0.2wgXbxRgxImVpTpMY7e88lQ6UOfx_rzujaMWBriH5RU"}, {"key": "api-localhost", "value": "http://localhost:3000/api", "type": "string"}, {"key": "api-WebRoutes", "value": "http://localhost:3000", "type": "string", "disabled": true}, {"key": "api-localhost", "value": "https://tixpi-dev-api-3fg33.ondigitalocean.app/api", "type": "string", "disabled": true}]}