{"name": "tixpi", "version": "1.0.0", "description": "**Markdown parser and generator written in <PERSON><PERSON><PERSON>**", "main": "src/index.js", "dependencies": {"@aws-sdk/client-location": "^3.830.0", "@aws-sdk/credential-providers": "^3.830.0", "@types/express": "^4.17.9", "@types/sequelize": "^4.28.9", "axios": "^0.22.0", "bcrypt": "^5.0.1", "body-parser": "^1.19.0", "cls-hooked": "^4.2.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^2.28.0", "dotenv": "^8.2.0", "express": "^4.17.1", "glob": "^7.1.6", "handlebars": "^4.7.7", "htmlparser2": "^10.0.0", "http": "0.0.1-security", "https": "^1.0.0", "i18next": "^24.1.0", "i18next-express-middleware": "^2.0.0", "i18next-fs-backend": "^2.6.0", "i18next-http-backend": "^3.0.1", "i18next-locize-backend": "^7.0.1", "iban": "^0.0.14", "joi": "^17.13.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "meilisearch": "^0.50.0", "moment": "^2.30.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.14", "pg": "^8.5.1", "pg-boss": "^10.1.6", "save": "^2.4.0", "sequelize": "^6.4.0", "sequelize-cli": "^6.2.0", "stripe": "^17.7.0", "uuid": "^8.3.2", "winston": "^3.14.2", "winston-syslog": "^2.7.1", "xstate": "^5.19.2"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/node": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/register": "^7.25.9", "copyfiles": "^2.4.1", "jest": "^26.6.3", "nodemon": "^3.1.9", "prettier": "^3.3.3", "supertest": "^6.1.3"}, "scripts": {"test": "cross-env NODE_ENV=test jest --verbose --silent --detectO<PERSON><PERSON>andles", "watch": "nodemon --exec babel-node src/index.js", "build": "npx babel src --out-dir dist && npx copyfiles -u 1 'src/**/*.html' dist/", "serve": "node dist/index.js", "db:create": "sequelize db:create", "db:drop": "sequelize db:drop", "db:reset:full": "sequelize db:drop && sequelize db:create && sequelize db:migrate && sequelize db:seed:all", "migrate": "sequelize db:migrate", "migrate:make": "sequelize migration:generate --name", "migrate:reset": "sequelize db:migrate:undo:all", "migrate:reset:one": "sequelize db:migrate:undo", "seed": "sequelize db:seed:all", "seed:reset": "sequelize db:seed:undo:all", "seed:one": "sequelize db:seed --seed", "sequelize": "sequelize"}, "jest": {"testEnvironment": "node"}, "repository": {"type": "git", "url": "git+https://gitlab.com/vivid-imagination/vi-backend-boilerplate.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://gitlab.com/vivid-imagination/vi-backend-boilerplate/issues"}, "homepage": "https://gitlab.com/vivid-imagination/vi-backend-boilerplate#readme", "nodemonConfig": {"ignore": ["node_modules", ".docker", ".vscode", ".idea", "deploy"]}, "engines": {"node": "~20.16.0", "npm": "~10.8.1"}}