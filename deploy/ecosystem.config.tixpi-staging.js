module.exports = {
  apps: [
    {
      name: 'tixpi',
      script: 'index.js',
      env: {
        PORT: 3000,
        HTTPS: true,
        NODE_ENV: 'staging',
        DOMAIN: 'dev-tixpi-api.vitech.dev',
        SES_AWS_REGION: 'eu-central-1',
        SES_ACCESS_KEY_ID: '********************',
        SES_SECRET_ACCESS_KEY: 'bummvl35TCOlPInphL4ii+Y2msThxcfENCH8Gwye',
        EMAIL_SENDER: '<EMAIL>',
        PUBLIC_HOST_URL: 'https://dev-tixpi.vitech.dev',
      },
    },
  ],
  deploy: {
    production: {
      user: 'ubuntu',
      host: '**************',
      ref: 'origin/staging',
      repo: '**************:dafeocom/tixpi-backend.git',
      path: '/home/<USER>/tixpi-test-api',
      'post-deploy':
        'npm install && cp deploy/ecosystem.config.tixpi-staging.js ecosystem.config.js && NODE_ENV=staging npm run migrate && pm2 reload ecosystem.config.js --env staging && rm ecosystem.config.js',
    },
  },
};
