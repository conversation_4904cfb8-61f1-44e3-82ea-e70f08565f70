services:
  backend-api:
    build:
      context: .
      dockerfile: deploy/docker/Dockerfile
    container_name: tixpi_backend
    env_file: .env
    volumes:
      - .:/www
      - /www/node_modules
    ports:
      - '${API_PORT:-3000}:3000'
    depends_on:
      - postgres
  postgres:
    image: postgres
    container_name: tixpi_postgres
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: tixpi
    volumes:
      - ./.docker/data/postgresql:/var/lib/postgresql/data
    ports:
      - '${DB_PORT:-5432}:5432'
